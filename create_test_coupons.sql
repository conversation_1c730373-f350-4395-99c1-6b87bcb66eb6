-- Insert test coupons
INSERT INTO "Coupons" (
    "Code", "Name", "Description", "Type", "Value",
    "MinimumOrderAmount", "MaximumDiscountAmount", "StartDate", "EndDate",
    "UsageLimit", "UsageLimitPerUser", "UsageCount", "IsActive", "Currency",
    "ApplicableToAllProducts", "CreatedAt", "UpdatedAt", "CreatedBy"
) VALUES
('BIENVENUE', 'Coupon de bienvenue', 'Réduction de 10% pour les nouveaux clients', 1, 10.00, 50000, 100000, '2024-01-01', '2025-12-31', 1000, 1, 0, true, 'GNF', true, NOW(), NOW(), 'system'),
('REDUCTION20', 'Réduction 20%', 'Réduction de 20% sur votre commande', 1, 20.00, 100000, 200000, '2024-01-01', '2025-12-31', 500, 3, 0, true, 'GNF', true, NOW(), NOW(), 'system'),
('LIVRAISON', 'Livraison gratuite', 'Livraison gratuite pour toute commande', 2, 0.00, 200000, NULL, '2024-01-01', '2025-12-31', 2000, 5, 0, true, 'GNF', true, NOW(), NOW(), 'system')
ON CONFLICT ("Code") DO NOTHING;
