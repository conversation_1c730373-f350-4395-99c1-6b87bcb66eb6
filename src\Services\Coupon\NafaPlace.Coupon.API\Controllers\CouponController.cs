using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Coupon.Application.DTOs;
using NafaPlace.Coupon.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Coupon.API.Controllers;

[ApiController]
[Route("api/coupon")]
public class CouponController : ControllerBase
{
    private readonly ICouponService _couponService;
    private readonly ILogger<CouponController> _logger;

    public CouponController(ICouponService couponService, ILogger<CouponController> logger)
    {
        _couponService = couponService;
        _logger = logger;
    }

    [HttpGet]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<List<CouponDto>>> GetCoupons([FromQuery] int page = 1, [FromQuery] int pageSize = 20, [FromQuery] bool? isActive = null)
    {
        try
        {
            var coupons = await _couponService.GetCouponsAsync(page, pageSize, isActive);
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting coupons");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("active")]
    public async Task<ActionResult<List<CouponDto>>> GetActiveCoupons()
    {
        try
        {
            var coupons = await _couponService.GetActiveCouponsAsync();
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active coupons");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CouponDto>> GetCoupon(int id)
    {
        try
        {
            var coupon = await _couponService.GetCouponByIdAsync(id);
            if (coupon == null)
            {
                return NotFound("Coupon not found");
            }
            return Ok(coupon);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting coupon {CouponId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("code/{code}")]
    public async Task<ActionResult<CouponDto>> GetCouponByCode(string code)
    {
        try
        {
            var coupon = await _couponService.GetCouponByCodeAsync(code);
            if (coupon == null)
            {
                return NotFound("Coupon not found");
            }
            return Ok(coupon);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting coupon by code {Code}", code);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CouponDto>> CreateCoupon([FromBody] CreateCouponRequest request)
    {
        try
        {
            var createdBy = GetUserId();
            var coupon = await _couponService.CreateCouponAsync(request, createdBy);
            return CreatedAtAction(nameof(GetCoupon), new { id = coupon.Id }, coupon);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating coupon");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CouponDto>> UpdateCoupon(int id, [FromBody] UpdateCouponRequest request)
    {
        try
        {
            var updatedBy = GetUserId();
            var coupon = await _couponService.UpdateCouponAsync(id, request, updatedBy);
            return Ok(coupon);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating coupon {CouponId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteCoupon(int id)
    {
        try
        {
            var success = await _couponService.DeleteCouponAsync(id);
            if (!success)
            {
                return NotFound("Coupon not found");
            }
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting coupon {CouponId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("validate")]
    [Authorize]
    public async Task<ActionResult<CouponValidationResult>> ValidateCoupon([FromBody] ValidateCouponRequest request)
    {
        try
        {
            var result = await _couponService.ValidateCouponAsync(request.CouponCode, request.Cart);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating coupon {Code}", request.CouponCode);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("validate-internal")]
    public async Task<ActionResult<CouponValidationResult>> ValidateCouponInternal([FromBody] ValidateCouponRequest request)
    {
        try
        {
            var result = await _couponService.ValidateCouponAsync(request.CouponCode, request.Cart);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating coupon {Code}", request.CouponCode);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("apply")]
    [Authorize]
    public async Task<ActionResult<CouponApplicationResult>> ApplyCoupon([FromBody] ValidateCouponRequest request)
    {
        try
        {
            var result = await _couponService.ApplyCouponAsync(request.CouponCode, request.Cart);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying coupon {Code}", request.CouponCode);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("user/available")]
    [Authorize]
    public async Task<ActionResult<List<CouponDto>>> GetUserAvailableCoupons([FromBody] CartForCouponValidation cart)
    {
        try
        {
            var userId = GetUserId();
            cart.UserId = userId;
            var coupons = await _couponService.GetUserAvailableCouponsAsync(userId, cart);
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user available coupons");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeactivateCoupon(int id)
    {
        try
        {
            var success = await _couponService.DeactivateCouponAsync(id);
            if (!success)
            {
                return NotFound("Coupon not found");
            }
            return Ok(new { message = "Coupon deactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating coupon {CouponId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{id}/activate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ActivateCoupon(int id)
    {
        try
        {
            var success = await _couponService.ActivateCouponAsync(id);
            if (!success)
            {
                return NotFound("Coupon not found");
            }
            return Ok(new { message = "Coupon activated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating coupon {CouponId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{id}/stats")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<Application.DTOs.CouponStatsDto>> GetCouponStats(int id)
    {
        try
        {
            var stats = await _couponService.GetCouponStatsAsync(id);
            return Ok(stats);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting coupon stats {CouponId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("stats/usage")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<List<Application.DTOs.CouponUsageStatsDto>>> GetUsageStats([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _couponService.GetCouponUsageStatsAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage stats");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("cleanup-expired")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> CleanupExpiredCoupons()
    {
        try
        {
            var count = await _couponService.CleanupExpiredCouponsAsync();
            return Ok(new { message = $"{count} expired coupons deactivated" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired coupons");
            return StatusCode(500, "Internal server error");
        }
    }

    private string GetUserId()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            throw new UnauthorizedAccessException("User ID not found in token");
        }
        return userId;
    }
}

public class ValidateCouponRequest
{
    public string CouponCode { get; set; } = string.Empty;
    public CartForCouponValidation Cart { get; set; } = new();
}
