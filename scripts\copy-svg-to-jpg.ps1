$categoriesPath = "src/Web/NafaPlace.Web/wwwroot/images/categories"

Write-Host "Copie des fichiers SVG vers JPG..." -ForegroundColor Cyan

# Copier chaque fichier SVG vers JPG
Copy-Item "$categoriesPath\beauty.svg" "$categoriesPath\beauty.jpg"
Copy-Item "$categoriesPath\electronics.svg" "$categoriesPath\electronics.jpg"
Copy-Item "$categoriesPath\fashion.svg" "$categoriesPath\fashion.jpg"
Copy-Item "$categoriesPath\home.svg" "$categoriesPath\home.jpg"
Copy-Item "$categoriesPath\sports.svg" "$categoriesPath\sports.jpg"

Write-Host "Fichiers copies avec succes!" -ForegroundColor Green

# Lister les fichiers
Write-Host "`nFichiers dans le dossier:" -ForegroundColor Yellow
Get-ChildItem -Path $categoriesPath | ForEach-Object {
    Write-Host "  $($_.Name)" -ForegroundColor Gray
}
