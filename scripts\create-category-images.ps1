# Script pour creer des images de categories placeholder
Write-Host "Creation des images de categories..." -ForegroundColor Cyan

$categoriesPath = "src/Web/NafaPlace.Web/wwwroot/images/categories"

# Creer le dossier s'il n'existe pas
if (!(Test-Path $categoriesPath)) {
    New-Item -ItemType Directory -Path $categoriesPath -Force
    Write-Host "Dossier cree: $categoriesPath" -ForegroundColor Green
}

# Definir les categories et leurs couleurs
$categories = @{
    "home" = @{ Name = "Maison"; Color = "#FF6B6B"; Icon = "HOME" }
    "fashion" = @{ Name = "Mode"; Color = "#4ECDC4"; Icon = "FASHION" }
    "electronics" = @{ Name = "Electronique"; Color = "#45B7D1"; Icon = "TECH" }
    "sports" = @{ Name = "Sport"; Color = "#96CEB4"; Icon = "SPORT" }
    "beauty" = @{ Name = "Beaute"; Color = "#FFEAA7"; Icon = "BEAUTY" }
}

foreach ($category in $categories.Keys) {
    $categoryInfo = $categories[$category]
    $fileName = "$categoriesPath/$category.jpg"
    
    # Créer un SVG simple
    $svgContent = @"
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad$category" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:$($categoryInfo.Color);stop-opacity:1" />
      <stop offset="100%" style="stop-color:$($categoryInfo.Color);stop-opacity:0.7" />
    </linearGradient>
  </defs>
  <rect width="300" height="200" fill="url(#grad$category)" rx="10" ry="10"/>
  <text x="150" y="80" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="white">$($categoryInfo.Icon)</text>
  <text x="150" y="130" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="white" font-weight="bold">$($categoryInfo.Name)</text>
  <text x="150" y="160" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white" opacity="0.8">NafaPlace</text>
</svg>
"@
    
    # Sauvegarder le SVG (nous utiliserons SVG au lieu de JPG pour simplifier)
    $svgFileName = "$categoriesPath\$category.svg"
    $svgContent | Out-File -FilePath $svgFileName -Encoding UTF8

    Write-Host "Image creee: $category.svg ($($categoryInfo.Name))" -ForegroundColor Green
}

Write-Host "`nToutes les images de categories ont ete creees!" -ForegroundColor Green
Write-Host "Emplacement: $categoriesPath" -ForegroundColor Gray

# Lister les fichiers crees
Write-Host "`nFichiers crees:" -ForegroundColor Yellow
Get-ChildItem -Path $categoriesPath | ForEach-Object {
    Write-Host "  $($_.Name)" -ForegroundColor Gray
}
