using Microsoft.EntityFrameworkCore;
using NafaPlace.Delivery.Application.Services;
using NafaPlace.Delivery.Application.DTOs;
using NafaPlace.Delivery.Domain.Models;
using NafaPlace.Delivery.Infrastructure.Data;

namespace NafaPlace.Delivery.Infrastructure.Repositories;

public class DeliveryRepository : IDeliveryRepository
{
    private readonly DeliveryDbContext _context;

    public DeliveryRepository(DeliveryDbContext context)
    {
        _context = context;
    }

    public async Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync()
    {
        return await _context.DeliveryZones
            .Where(z => z.IsActive)
            .Select(z => new DeliveryZoneDto
            {
                Id = z.Id,
                Name = z.Name,
                Description = z.Description,
                BaseDeliveryFee = z.BaseDeliveryFee,
                FreeDeliveryThreshold = z.FreeDeliveryThreshold,
                EstimatedDeliveryDays = z.EstimatedDeliveryDays,
                IsActive = z.IsActive
            })
            .ToListAsync();
    }

    public async Task<DeliveryZoneDto?> GetDeliveryZoneByIdAsync(int zoneId)
    {
        var zone = await _context.DeliveryZones
            .Where(z => z.Id == zoneId && z.IsActive)
            .FirstOrDefaultAsync();

        if (zone == null) return null;

        return new DeliveryZoneDto
        {
            Id = zone.Id,
            Name = zone.Name,
            Description = zone.Description,
            BaseDeliveryFee = zone.BaseDeliveryFee,
            FreeDeliveryThreshold = zone.FreeDeliveryThreshold,
            EstimatedDeliveryDays = zone.EstimatedDeliveryDays,
            IsActive = zone.IsActive
        };
    }

    public async Task<DeliveryOrderDto> CreateDeliveryOrderAsync(CreateDeliveryOrderRequest request)
    {
        var deliveryOrder = new DeliveryOrder
        {
            OrderId = request.OrderId,
            TrackingNumber = GenerateTrackingNumber(),
            CustomerId = request.CustomerId,
            CustomerName = request.CustomerName,
            CustomerEmail = request.CustomerEmail,
            CustomerPhone = request.CustomerPhone,
            DeliveryAddress = request.DeliveryAddress,
            DeliveryCity = request.DeliveryCity,
            DeliveryRegion = request.DeliveryRegion,
            ZoneId = 1, // Default zone
            CarrierId = request.PreferredCarrierId ?? 1, // Default carrier
            DeliveryFee = 25000, // Default fee
            OrderValue = request.OrderValue,
            TotalFee = 25000 + request.OrderValue,
            Currency = "GNF",
            Weight = request.Weight,
            PackageCount = request.PackageCount,
            PackageDescription = request.PackageDescription,
            Type = request.Type,
            Status = DeliveryStatus.Pending,
            ScheduledDeliveryDate = request.ScheduledDeliveryDate,
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(2),
            DeliveryInstructions = request.DeliveryInstructions
        };

        _context.DeliveryOrders.Add(deliveryOrder);
        await _context.SaveChangesAsync();

        // Add initial tracking event
        var tracking = new DeliveryTracking
        {
            DeliveryOrderId = deliveryOrder.Id,
            Status = DeliveryStatus.Pending,
            Description = "Commande reçue et en cours de préparation",
            Location = "Centre de tri",
            Notes = "Commande reçue et en cours de préparation",
            EventDate = DateTime.UtcNow
        };

        _context.DeliveryTrackings.Add(tracking);
        await _context.SaveChangesAsync();

        return await GetDeliveryOrderByIdAsync(deliveryOrder.Id) ?? throw new InvalidOperationException("Failed to create delivery order");
    }

    public async Task<DeliveryOrderDto?> GetDeliveryOrderByIdAsync(int id)
    {
        var order = await _context.DeliveryOrders
            .Include(o => o.Zone)
            .Include(o => o.Carrier)
            .Include(o => o.TrackingEvents)
            .FirstOrDefaultAsync(o => o.Id == id);

        if (order == null) return null;

        return new DeliveryOrderDto
        {
            Id = order.Id,
            OrderId = order.OrderId,
            TrackingNumber = order.TrackingNumber,
            CustomerId = order.CustomerId,
            CustomerName = order.CustomerName,
            CustomerEmail = order.CustomerEmail,
            CustomerPhone = order.CustomerPhone,
            DeliveryAddress = order.DeliveryAddress,
            DeliveryCity = order.DeliveryCity,
            DeliveryRegion = order.DeliveryRegion,
            ZoneId = order.ZoneId,
            ZoneName = order.Zone?.Name ?? "",
            CarrierId = order.CarrierId,
            CarrierName = order.Carrier?.Name ?? "",
            DeliveryFee = order.DeliveryFee,
            TotalFee = order.TotalFee,
            Currency = order.Currency,
            OrderValue = order.OrderValue,
            Weight = order.Weight,
            PackageCount = order.PackageCount,
            Type = order.Type,
            Status = order.Status,
            ScheduledDeliveryDate = order.ScheduledDeliveryDate,
            EstimatedDeliveryDate = order.EstimatedDeliveryDate,
            ActualDeliveryDate = order.ActualDeliveryDate,
            DeliveryInstructions = order.DeliveryInstructions,
            DeliveryAttempts = order.DeliveryAttempts,
            CustomerRating = order.CustomerRating,
            CustomerFeedback = order.CustomerFeedback,
            CreatedAt = order.CreatedAt,
            TrackingEvents = order.TrackingEvents.Select(t => new DeliveryTrackingDto
            {
                Id = t.Id,
                DeliveryOrderId = t.DeliveryOrderId,
                Status = t.Status,
                Description = t.Description,
                Location = t.Location,
                Latitude = t.Latitude,
                Longitude = t.Longitude,
                EventDate = t.EventDate,
                EventBy = t.EventBy,
                Notes = t.Notes,
                PhotoUrl = t.PhotoUrl,
                IsCustomerVisible = t.IsCustomerVisible,
                IsAutomated = t.IsAutomated
            }).OrderByDescending(t => t.EventDate).ToList()
        };
    }

    public async Task<List<DeliveryTrackingDto>> GetDeliveryTrackingByTrackingNumberAsync(string trackingNumber)
    {
        var order = await _context.DeliveryOrders
            .Include(o => o.TrackingEvents)
            .FirstOrDefaultAsync(o => o.TrackingNumber == trackingNumber);

        if (order == null) return new List<DeliveryTrackingDto>();

        return order.TrackingEvents.Select(t => new DeliveryTrackingDto
        {
            Id = t.Id,
            DeliveryOrderId = t.DeliveryOrderId,
            Status = t.Status,
            Description = t.Description,
            Location = t.Location,
            Latitude = t.Latitude,
            Longitude = t.Longitude,
            EventDate = t.EventDate,
            EventBy = t.EventBy,
            Notes = t.Notes,
            PhotoUrl = t.PhotoUrl,
            IsCustomerVisible = t.IsCustomerVisible,
            IsAutomated = t.IsAutomated
        }).OrderByDescending(t => t.EventDate).ToList();
    }

    public async Task<bool> UpdateDeliveryStatusAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request)
    {
        var order = await _context.DeliveryOrders.FindAsync(deliveryOrderId);
        if (order == null) return false;

        order.Status = request.Status;
        if (request.Status == DeliveryStatus.Delivered)
        {
            order.ActualDeliveryDate = DateTime.UtcNow;
        }

        // Add tracking event
        var tracking = new DeliveryTracking
        {
            DeliveryOrderId = deliveryOrderId,
            Status = request.Status,
            Description = request.Description,
            Location = request.Location ?? "",
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            EventDate = DateTime.UtcNow,
            EventBy = request.EventBy,
            Notes = request.Notes ?? "",
            PhotoUrl = request.PhotoUrl,
            IsCustomerVisible = request.IsCustomerVisible
        };

        _context.DeliveryTrackings.Add(tracking);
        await _context.SaveChangesAsync();

        return true;
    }

    private string GenerateTrackingNumber()
    {
        return $"NP{DateTime.UtcNow:yyyyMMdd}{Random.Shared.Next(1000, 9999)}";
    }
}
