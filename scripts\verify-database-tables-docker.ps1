# Script de vérification des tables de base de données NafaPlace via Docker
# Ce script utilise Docker pour se connecter aux bases de données PostgreSQL

Write-Host "🔍 Vérification des tables de base de données NafaPlace via Docker..." -ForegroundColor Cyan

# Configuration des bases de données
$databases = @{
    "Identity" = @{ Container = "nafaplace-identity-db"; Database = "NafaPlace.Identity" }
    "Catalog" = @{ Container = "nafaplace-catalog-db"; Database = "NafaPlace.Catalog" }
    "Order" = @{ Container = "nafaplace-order-db"; Database = "NafaPlace.Order" }
    "Reviews" = @{ Container = "nafaplace-reviews-db"; Database = "NafaPlace.Reviews" }
    "Notifications" = @{ Container = "nafaplace-notifications-db"; Database = "NafaPlace.Notifications" }
    "Wishlist" = @{ Container = "nafaplace-wishlist-db"; Database = "NafaPlace.Wishlist" }
    "Inventory" = @{ Container = "nafaplace-inventory-db"; Database = "NafaPlace.Inventory" }
    "Coupon" = @{ Container = "nafaplace-coupon-db"; Database = "NafaPlace.Coupon" }
    "Delivery" = @{ Container = "nafaplace-delivery-db"; Database = "NafaPlace.Delivery" }
}

# Liste des tables attendues par service
$expectedTables = @{
    "Identity" = @("Users", "Roles", "UserRoles", "RefreshTokens", "__EFMigrationsHistory")
    "Catalog" = @("Categories", "Products", "ProductImages", "ProductVariants", "ProductAttributes", "ProductVariantAttributes", "Sellers", "__EFMigrationsHistory")
    "Order" = @("Orders", "OrderItems", "__EFMigrationsHistory")
    "Inventory" = @("StockReservations", "StockAlerts", "StockAlertNotifications", "StockMovements", "__EFMigrationsHistory")
    "Notifications" = @("Notifications", "NotificationTemplates", "__EFMigrationsHistory")
    "Delivery" = @("DeliveryZones", "Carriers", "DeliveryOrders", "DeliveryTrackings", "__EFMigrationsHistory")
    "Coupon" = @("Coupons", "__EFMigrationsHistory")
    "Wishlist" = @("WishlistItems", "__EFMigrationsHistory")
    "Reviews" = @("Reviews", "__EFMigrationsHistory")
}

# Fonction pour exécuter une requête SQL via Docker
function Invoke-DockerSqlQuery {
    param(
        [string]$Query,
        [string]$Container,
        [string]$Database
    )
    
    try {
        $result = & docker exec $Container psql -U postgres -d $Database -t -c $Query 2>&1
        if ($LASTEXITCODE -eq 0) {
            return $result
        } else {
            return $null
        }
    }
    catch {
        return $null
    }
}

Write-Host "`n📋 Tables attendues par service:" -ForegroundColor Yellow
foreach ($service in $expectedTables.Keys) {
    Write-Host "  🔹 $service : $($expectedTables[$service].Count) tables" -ForegroundColor Gray
}

# Vérifier la connexion à chaque base de données
Write-Host "`n🔌 Test de connexion aux bases de données..." -ForegroundColor Cyan
$connectionResults = @{}
foreach ($service in $databases.Keys) {
    $dbConfig = $databases[$service]
    $connectionTest = Invoke-DockerSqlQuery "SELECT 1;" $dbConfig.Container $dbConfig.Database
    $connectionResults[$service] = ($null -ne $connectionTest)
    
    if ($connectionResults[$service]) {
        Write-Host "  ✅ $service ($($dbConfig.Container))" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $service ($($dbConfig.Container))" -ForegroundColor Red
    }
}

$connectedDatabases = ($connectionResults.Values | Where-Object { $_ }).Count
Write-Host "`n📊 Bases de données connectées: $connectedDatabases/$($databases.Count)" -ForegroundColor $(if ($connectedDatabases -eq $databases.Count) { "Green" } else { "Yellow" })

# Récupérer la liste des tables pour chaque base de données
Write-Host "`n📊 Récupération des tables par base de données..." -ForegroundColor Cyan
$existingTablesQuery = @"
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;
"@

$allExistingTables = @{}
foreach ($service in $databases.Keys) {
    if ($connectionResults[$service]) {
        $dbConfig = $databases[$service]
        $existingTablesResult = Invoke-DockerSqlQuery $existingTablesQuery $dbConfig.Container $dbConfig.Database
        
        if ($null -ne $existingTablesResult) {
            $existingTables = $existingTablesResult | Where-Object { $_.Trim() -ne "" } | ForEach-Object { $_.Trim() }
            $allExistingTables[$service] = $existingTables
            Write-Host "  📋 $service : $($existingTables.Count) tables trouvées" -ForegroundColor Gray
        } else {
            $allExistingTables[$service] = @()
            Write-Host "  ❌ $service : Impossible de récupérer les tables" -ForegroundColor Red
        }
    } else {
        $allExistingTables[$service] = @()
        Write-Host "  ❌ $service : Base de données non accessible" -ForegroundColor Red
    }
}

# Vérifier chaque service
$totalExpected = 0
$totalFound = 0
$missingTables = @()
$extraTables = @()

Write-Host "`n🔍 Vérification par service:" -ForegroundColor Cyan

foreach ($service in $expectedTables.Keys) {
    Write-Host "`n🔹 Service: $service" -ForegroundColor Yellow
    
    $serviceExpected = $expectedTables[$service]
    $serviceExisting = $allExistingTables[$service]
    $serviceFound = 0
    $serviceMissing = @()
    
    if ($connectionResults[$service]) {
        foreach ($table in $serviceExpected) {
            $totalExpected++
            if ($serviceExisting -contains $table) {
                Write-Host "  ✅ $table" -ForegroundColor Green
                $serviceFound++
                $totalFound++
            } else {
                Write-Host "  ❌ $table (MANQUANTE)" -ForegroundColor Red
                $serviceMissing += $table
                $missingTables += "$service" + "." + "$table"
            }
        }
        
        # Identifier les tables supplémentaires dans ce service
        foreach ($table in $serviceExisting) {
            if ($serviceExpected -notcontains $table) {
                $extraTables += "$service" + "." + "$table"
                Write-Host "  ➕ $table (supplémentaire)" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "  ❌ Base de données non accessible" -ForegroundColor Red
        foreach ($table in $serviceExpected) {
            $totalExpected++
            $missingTables += "$service.$table"
        }
    }
    
    $servicePercentage = if ($serviceExpected.Count -gt 0) { [math]::Round(($serviceFound / $serviceExpected.Count) * 100, 1) } else { 0 }
    Write-Host "  📊 $serviceFound/$($serviceExpected.Count) tables trouvées ($servicePercentage%)" -ForegroundColor $(if ($serviceFound -eq $serviceExpected.Count) { "Green" } else { "Yellow" })
    
    if ($serviceMissing.Count -gt 0) {
        Write-Host "  ⚠️  Tables manquantes: $($serviceMissing -join ', ')" -ForegroundColor Red
    }
}

# Résumé final
Write-Host "`n📊 RÉSUMÉ FINAL" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

$overallPercentage = if ($totalExpected -gt 0) { [math]::Round(($totalFound / $totalExpected) * 100, 1) } else { 0 }
Write-Host "📋 Tables attendues: $totalExpected" -ForegroundColor Gray
Write-Host "✅ Tables trouvées: $totalFound" -ForegroundColor $(if ($totalFound -eq $totalExpected) { "Green" } else { "Yellow" })
Write-Host "❌ Tables manquantes: $($missingTables.Count)" -ForegroundColor $(if ($missingTables.Count -eq 0) { "Green" } else { "Red" })
Write-Host "➕ Tables supplémentaires: $($extraTables.Count)" -ForegroundColor $(if ($extraTables.Count -eq 0) { "Green" } else { "Yellow" })
Write-Host "📊 Pourcentage de complétude: $overallPercentage%" -ForegroundColor $(if ($overallPercentage -eq 100) { "Green" } else { "Yellow" })

if ($missingTables.Count -gt 0) {
    Write-Host "`n❌ TABLES MANQUANTES:" -ForegroundColor Red
    foreach ($table in $missingTables) {
        Write-Host "  - $table" -ForegroundColor Red
    }
}

if ($extraTables.Count -gt 0) {
    Write-Host "`n➕ TABLES SUPPLÉMENTAIRES (non attendues):" -ForegroundColor Yellow
    foreach ($table in $extraTables) {
        Write-Host "  - $table" -ForegroundColor Yellow
    }
}

# Vérifier les migrations pour chaque base de données
Write-Host "`n🔄 Vérification des migrations..." -ForegroundColor Cyan
$migrationQuery = @"
SELECT COUNT(*) as migration_count 
FROM "__EFMigrationsHistory"
WHERE "MigrationId" IS NOT NULL;
"@

$totalMigrations = 0
foreach ($service in $databases.Keys) {
    if ($connectionResults[$service]) {
        $dbConfig = $databases[$service]
        $migrationResult = Invoke-DockerSqlQuery $migrationQuery $dbConfig.Container $dbConfig.Database
        if ($migrationResult) {
            $migrationCount = $migrationResult.Trim()
            $totalMigrations += [int]$migrationCount
            Write-Host "  📊 $service : $migrationCount migrations" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  $service : Impossible de vérifier les migrations" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ❌ $service : Base de données non accessible" -ForegroundColor Red
    }
}

Write-Host "📊 Nombre total de migrations appliquées: $totalMigrations" -ForegroundColor Green

# Statut final
if ($totalFound -eq $totalExpected -and $missingTables.Count -eq 0) {
    Write-Host "`n🎉 SUCCÈS: Toutes les tables attendues sont présentes!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n⚠️  ATTENTION: Des tables sont manquantes ou des problèmes ont été détectés" -ForegroundColor Yellow
    exit 1
}
