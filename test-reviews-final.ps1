# Test final pour vérifier l'ajout de commentaires et avis
Write-Host "🧪 Test Final - Ajout de Commentaires et Avis" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Configuration
$identityUrl = "http://localhost:5155"
$reviewsUrl = "http://localhost:5006"
$catalogUrl = "http://localhost:5243"
$wishlistUrl = "http://localhost:5008"

# Fonction pour créer un utilisateur de test
function Create-TestUser {
    param($username, $email, $password)
    
    $registerData = @{
        username = $username
        email = $email
        password = $password
        firstName = "Test"
        lastName = "User"
        phoneNumber = "+224123456789"
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$identityUrl/api/auth/register" -Method POST -Body $registerData -ContentType "application/json"
        return $true
    }
    catch {
        return $false
    }
}

# Fonction pour se connecter
function Login-User {
    param($identifier, $password)
    
    $loginData = @{
        identifier = $identifier
        password = $password
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$identityUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
        return $response
    }
    catch {
        Write-Host "   Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Créer un utilisateur de test unique
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testUser = "testuser$timestamp"
$testEmail = "test$<EMAIL>"
$testPassword = "Test123!"

Write-Host "`n1. 👤 Création d'un utilisateur de test..." -ForegroundColor Cyan
Write-Host "   Username: $testUser" -ForegroundColor Gray
Write-Host "   Email: $testEmail" -ForegroundColor Gray

$userCreated = Create-TestUser -username $testUser -email $testEmail -password $testPassword

if ($userCreated) {
    Write-Host "✅ Utilisateur créé avec succès" -ForegroundColor Green
} else {
    Write-Host "⚠️ Utilisateur peut-être déjà existant, continuons..." -ForegroundColor Yellow
}

# Attendre un peu pour que l'utilisateur soit bien créé
Start-Sleep -Seconds 3

Write-Host "`n2. 🔐 Connexion de l'utilisateur..." -ForegroundColor Cyan

$loginResponse = Login-User -identifier $testUser -password $testPassword

if ($loginResponse -and $loginResponse.accessToken) {
    Write-Host "✅ Connexion réussie !" -ForegroundColor Green
    Write-Host "   User ID: $($loginResponse.user.id)" -ForegroundColor Gray
    Write-Host "   Username: $($loginResponse.user.username)" -ForegroundColor Gray
    Write-Host "   Token: $($loginResponse.accessToken.Substring(0, 50))..." -ForegroundColor Gray
    
    $token = $loginResponse.accessToken
    $userId = $loginResponse.user.id
    $username = $loginResponse.user.username
    
    # Headers d'authentification
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    Write-Host "`n3. 📦 Récupération des produits..." -ForegroundColor Cyan
    
    try {
        $products = Invoke-RestMethod -Uri "$catalogUrl/api/products" -Method GET
        
        if ($products -and $products.Count -gt 0) {
            $product = $products[0]
            $productId = $product.id
            $productName = $product.name
            
            Write-Host "✅ Produits récupérés !" -ForegroundColor Green
            Write-Host "   Produit de test: $productName (ID: $productId)" -ForegroundColor Gray
            
            Write-Host "`n4. ⭐ Test de création d'avis..." -ForegroundColor Cyan
            
            # Données pour créer un avis
            $reviewData = @{
                productId = [int]$productId
                userId = $userId.ToString()
                userName = $username
                rating = 5
                title = "Excellent produit - Test automatique"
                comment = "Ceci est un test automatique pour vérifier que les corrections JWT fonctionnent. Le produit est vraiment excellent !"
                isVerifiedPurchase = $false
            } | ConvertTo-Json
            
            Write-Host "   Données de l'avis:" -ForegroundColor Gray
            Write-Host "   - Produit ID: $productId" -ForegroundColor Gray
            Write-Host "   - User ID: $userId" -ForegroundColor Gray
            Write-Host "   - Username: $username" -ForegroundColor Gray
            Write-Host "   - Rating: 5/5" -ForegroundColor Gray
            
            try {
                Write-Host "`n   Envoi de l'avis à l'API Reviews..." -ForegroundColor Gray
                $reviewResponse = Invoke-RestMethod -Uri "$reviewsUrl/api/reviews" -Method POST -Body $reviewData -Headers $headers
                
                Write-Host "✅ SUCCÈS ! Avis créé avec succès !" -ForegroundColor Green
                Write-Host "   Review ID: $($reviewResponse.id)" -ForegroundColor Gray
                Write-Host "   Titre: $($reviewResponse.title)" -ForegroundColor Gray
                Write-Host "   Note: $($reviewResponse.rating)/5" -ForegroundColor Gray
                Write-Host "   Statut: $($reviewResponse.status)" -ForegroundColor Gray
                Write-Host "   Date: $($reviewResponse.createdAt)" -ForegroundColor Gray
                
                Write-Host "`n5. 🔍 Vérification de l'avis créé..." -ForegroundColor Cyan
                
                # Vérifier que l'avis apparaît dans la liste
                try {
                    Start-Sleep -Seconds 2  # Attendre un peu
                    $productReviews = Invoke-RestMethod -Uri "$reviewsUrl/api/reviews/product/$productId" -Method GET
                    
                    $ourReview = $productReviews.reviews | Where-Object { $_.id -eq $reviewResponse.id }
                    
                    if ($ourReview) {
                        Write-Host "✅ Avis trouvé dans la liste des avis du produit !" -ForegroundColor Green
                        Write-Host "   Statut dans la liste: $($ourReview.status)" -ForegroundColor Gray
                        Write-Host "   Commentaire: $($ourReview.comment.Substring(0, 50))..." -ForegroundColor Gray
                    } else {
                        Write-Host "⚠️ Avis créé mais pas encore visible (modération en cours)" -ForegroundColor Yellow
                    }
                    
                    # Test du résumé des avis
                    $summary = Invoke-RestMethod -Uri "$reviewsUrl/api/reviews/product/$productId/summary" -Method GET
                    Write-Host "   Résumé des avis - Total: $($summary.totalReviews), Moyenne: $($summary.averageRating)" -ForegroundColor Gray
                    
                }
                catch {
                    Write-Host "⚠️ Erreur lors de la vérification: $($_.Exception.Message)" -ForegroundColor Yellow
                }
                
                Write-Host "`n6. 💝 Test bonus - Wishlist..." -ForegroundColor Cyan
                
                try {
                    # Test du compteur wishlist
                    $wishlistCount = Invoke-RestMethod -Uri "$wishlistUrl/api/wishlist/count" -Method GET -Headers $headers
                    Write-Host "✅ Wishlist accessible ! Nombre d'articles: $wishlistCount" -ForegroundColor Green
                    
                    # Test d'ajout à la wishlist
                    $wishlistData = @{
                        productId = [int]$productId
                    } | ConvertTo-Json
                    
                    try {
                        $addResponse = Invoke-RestMethod -Uri "$wishlistUrl/api/wishlist/add" -Method POST -Body $wishlistData -Headers $headers
                        Write-Host "✅ Produit ajouté à la wishlist !" -ForegroundColor Green
                    }
                    catch {
                        Write-Host "⚠️ Produit déjà dans la wishlist" -ForegroundColor Yellow
                    }
                    
                }
                catch {
                    Write-Host "❌ Erreur avec la wishlist: $($_.Exception.Message)" -ForegroundColor Red
                }
                
            }
            catch {
                Write-Host "❌ ÉCHEC ! Erreur lors de la création de l'avis" -ForegroundColor Red
                Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
                
                if ($_.Exception.Response) {
                    $statusCode = $_.Exception.Response.StatusCode
                    Write-Host "   Code de statut: $statusCode" -ForegroundColor Red
                    
                    if ($statusCode -eq 401) {
                        Write-Host "   ⚠️ Erreur 401: Problème d'authentification JWT détecté" -ForegroundColor Yellow
                        Write-Host "   Les corrections JWT ne fonctionnent pas encore" -ForegroundColor Yellow
                    }
                }
            }
            
        } else {
            Write-Host "❌ Aucun produit trouvé pour tester" -ForegroundColor Red
        }
        
    }
    catch {
        Write-Host "❌ Erreur lors de la récupération des produits: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ Échec de la connexion" -ForegroundColor Red
    Write-Host "   Impossible de tester les avis sans authentification" -ForegroundColor Red
}

Write-Host "`n🏁 Test terminé !" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

Write-Host "`n📊 Résumé:" -ForegroundColor Yellow
Write-Host "- Si vous voyez '✅ SUCCÈS ! Avis créé avec succès !'" -ForegroundColor White
Write-Host "  alors les corrections JWT fonctionnent parfaitement !" -ForegroundColor Green
Write-Host "- Si vous voyez '❌ ÉCHEC ! Erreur 401'" -ForegroundColor White
Write-Host "  alors il faut encore investiguer le problème JWT" -ForegroundColor Red
