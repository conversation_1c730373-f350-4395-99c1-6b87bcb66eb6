# Test de creation d'avis

# 1. Connexion pour obtenir un token
$loginBody = @{
    email = "<EMAIL>"
    password = "Kouyate92."
} | ConvertTo-Json

Write-Host "Connexion en cours..."
try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $loginBody
    $token = $loginResponse.token
    Write-Host "Connexion reussie, token obtenu"
} catch {
    Write-Host "Erreur de connexion: $($_.Exception.Message)"
    exit 1
}

# 2. Test de creation d'avis
$reviewBody = @{
    productId = 1
    userId = "4"
    userName = "lamine92"
    rating = 5
    title = "Test d'avis automatique"
    comment = "Ceci est un test automatique de creation d'avis"
    isVerifiedPurchase = $false
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Test de l'API Coupon d'abord
Write-Host "Test de l'API Coupon..."
try {
    $couponResponse = Invoke-RestMethod -Uri "http://localhost:5007/api/coupons" -Method GET -Headers $headers
    Write-Host "API Coupon fonctionne! Nombre de coupons: $($couponResponse.Count)"
} catch {
    Write-Host "Erreur API Coupon: $($_.Exception.Message)"
}

Write-Host "Creation d'avis en cours..."
try {
    $reviewResponse = Invoke-RestMethod -Uri "http://localhost:5006/api/reviews" -Method POST -Headers $headers -Body $reviewBody
    Write-Host "Avis cree avec succes!"
    Write-Host "Reponse: $($reviewResponse | ConvertTo-Json -Depth 3)"
} catch {
    Write-Host "Erreur de creation d'avis: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Code de statut: $statusCode"
    }
}
