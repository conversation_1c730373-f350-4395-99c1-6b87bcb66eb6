@page "/checkout"
@using NafaPlace.Web.Models.Order
@using System.Security.Claims

@inject NavigationManager NavigationManager
<h1>Passer la commande</h1>

<div class="row">
    <div class="col-md-8">
        <h3>Adress<PERSON> de l<PERSON></h3>
        <EditForm Model="@_orderRequest" OnValidSubmit="SubmitOrder">
            <DataAnnotationsValidator />
            <ValidationSummary />

            <div class="mb-3">
                <label class="form-label">Nom complet</label>
                <InputText class="form-control" @bind-Value="_orderRequest.ShippingAddress.FullName" />
            </div>
            <div class="mb-3">
                <label class="form-label">Adresse</label>
                <InputText class="form-control" @bind-Value="_orderRequest.ShippingAddress.Address" />
            </div>
            <div class="mb-3">
                <label class="form-label">Ville</label>
                <InputText class="form-control" @bind-Value="_orderRequest.ShippingAddress.City" />
            </div>
            <div class="mb-3">
                <label class="form-label">Pays</label>
                <InputText class="form-control" @bind-Value="_orderRequest.ShippingAddress.Country" />
            </div>
            <div class="mb-3">
                <label class="form-label">Code postal</label>
                <InputText class="form-control" @bind-Value="_orderRequest.ShippingAddress.PostalCode" />
            </div>
            <div class="mb-3">
                <label class="form-label">Téléphone</label>
                <InputText class="form-control" @bind-Value="_orderRequest.ShippingAddress.PhoneNumber" />
            </div>

            <hr class="my-4">

            <h3>Mode de paiement</h3>
            <div class="mb-4">
                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="paymentMethod" id="cashOnDelivery"
                           value="CashOnDelivery" @onchange="@(() => OnPaymentMethodChanged("CashOnDelivery"))"
                           checked="@(_orderRequest.PaymentMethod == "CashOnDelivery")">
                    <label class="form-check-label" for="cashOnDelivery">
                        <i class="fas fa-money-bill-wave me-2 text-success"></i>
                        <strong>Paiement à la livraison</strong>
                        <small class="d-block text-muted">Payez en espèces lors de la réception de votre commande</small>
                    </label>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="paymentMethod" id="orangeMoney"
                           value="OrangeMoney" @onchange="@(() => OnPaymentMethodChanged("OrangeMoney"))"
                           checked="@(_orderRequest.PaymentMethod == "OrangeMoney")">
                    <label class="form-check-label" for="orangeMoney">
                        <i class="fas fa-mobile-alt me-2 text-warning"></i>
                        <strong>Orange Money</strong>
                        <small class="d-block text-muted">Paiement mobile sécurisé avec Orange Money</small>
                    </label>
                </div>

                @if (_orderRequest.PaymentMethod == "OrangeMoney")
                {
                    <div class="ms-4 mb-3">
                        <label for="orangePhone" class="form-label">Numéro Orange Money</label>
                        <InputText id="orangePhone" class="form-control" @bind-Value="_orderRequest.PhoneNumber"
                                   placeholder="+224 6XX XX XX XX" />
                        <small class="form-text text-muted">Entrez votre numéro Orange Money pour le paiement</small>
                    </div>
                }

                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="paymentMethod" id="stripe"
                           value="Stripe" @onchange="@(() => OnPaymentMethodChanged("Stripe"))"
                           checked="@(_orderRequest.PaymentMethod == "Stripe")">
                    <label class="form-check-label" for="stripe">
                        <i class="fab fa-cc-stripe me-2 text-primary"></i>
                        <strong>Carte bancaire</strong>
                        <small class="d-block text-muted">Paiement sécurisé par carte bancaire via Stripe</small>
                    </label>
                </div>
            </div>

            <button type="submit" class="btn btn-primary btn-lg w-100" disabled="@_isProcessing">
                @if (_isProcessing)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    <span>Traitement en cours...</span>
                }
                else
                {
                    <i class="fas fa-credit-card me-2"></i>
                    <span>Valider et payer</span>
                }
            </button>
        </EditForm>
    </div>
    <div class="col-md-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    Résumé de la commande
                </h5>
            </div>
            <div class="card-body">
                @if (_cart != null && _cart.Items.Any())
                {
                    <!-- Articles -->
                    <div class="mb-3">
                        @foreach (var item in _cart.Items)
                        {
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <h6 class="mb-0">@item.ProductName</h6>
                                    <small class="text-muted">@item.UnitPrice.ToString("N0") GNF × @item.Quantity</small>
                                </div>
                                <span class="fw-bold">@((item.UnitPrice * item.Quantity).ToString("N0")) GNF</span>
                            </div>
                        }
                    </div>

                    <hr>

                    <!-- Calculs -->
                    <div class="order-summary">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Sous-total (@_cart.Items.Sum(i => i.Quantity) articles)</span>
                            <span>@GetSubTotal().ToString("N0") GNF</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Frais de livraison</span>
                            <span class="text-success">
                                @if (GetSubTotal() >= 500000)
                                {
                                    <span>Gratuit</span>
                                }
                                else
                                {
                                    <span>25 000 GNF</span>
                                }
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>TVA (18%)</span>
                            <span>@GetTax().ToString("N0") GNF</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total à payer</strong>
                            <strong class="text-primary fs-5">@GetTotal().ToString("N0") GNF</strong>
                        </div>
                    </div>
                }
                else
                {
                    <div class="text-center text-muted">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>Votre panier est vide</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@code {
    private OrderCreateDto _orderRequest = new()
    {
        PaymentMethod = "CashOnDelivery",
        ShippingAddress = new ShippingAddressDto()
    };
    private CartDto? _cart;
    private string _userId = string.Empty;
    private bool _isProcessing = false;

    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; } = null!;

    [Inject]
    private ICartService CartService { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateTask;
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
        }
        else
        {
            // Pour le checkout, rediriger vers la page de connexion si pas connecté
            NavigationManager.NavigateTo("/auth/login?returnUrl=" + Uri.EscapeDataString("/checkout"));
            return;
        }

        if (!string.IsNullOrEmpty(_userId))
        {
            _cart = await CartService.GetCartAsync(_userId);
        }
    }

    [Inject]
    private IOrderService OrderService { get; set; } = null!;

    private void OnPaymentMethodChanged(string paymentMethod)
    {
        _orderRequest.PaymentMethod = paymentMethod;
        StateHasChanged();
    }

    private async Task SubmitOrder()
    {
        try
        {
            _isProcessing = true;

            if (_cart == null || !_cart.Items.Any())
            {
                NavigationManager.NavigateTo("/cart");
                return;
            }

            // Validation spécifique pour Orange Money
            if (_orderRequest.PaymentMethod == "OrangeMoney" && string.IsNullOrEmpty(_orderRequest.PhoneNumber))
            {
                Console.WriteLine("Numéro Orange Money requis");
                return;
            }

            _orderRequest.UserId = _userId;

            // Créer la commande via l'API Order checkout endpoint
            var response = await OrderService.CheckoutAsync(_orderRequest);
            if (response != null)
            {
                // Rediriger selon le mode de paiement
                switch (_orderRequest.PaymentMethod)
                {
                    case "OrangeMoney":
                        NavigationManager.NavigateTo($"/payment/orange-money/{response.Id}");
                        break;
                    case "Stripe":
                        NavigationManager.NavigateTo($"/payment/stripe/{response.Id}");
                        break;
                    default:
                        NavigationManager.NavigateTo($"/order-confirmation/{response.Id}");
                        break;
                }
            }
            else
            {
                Console.WriteLine("Erreur lors de la création de la commande");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la soumission de la commande: {ex.Message}");
        }
        finally
        {
            _isProcessing = false;
        }
    }

    private decimal GetSubTotal()
    {
        return _cart?.Items?.Sum(i => i.UnitPrice * i.Quantity) ?? 0;
    }

    private decimal GetShippingFee()
    {
        var subTotal = GetSubTotal();
        return subTotal > 500000 ? 0 : 25000;
    }

    private decimal GetTax()
    {
        return GetSubTotal() * 0.18m;
    }

    private decimal GetTotal()
    {
        return GetSubTotal() + GetShippingFee() + GetTax();
    }
}