using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Application.DTOs;

namespace NafaPlace.Catalog.API.Controllers;
    [ApiController]
    [Route("api/v1/[controller]")]
    public class ProductsController : ControllerBase
    {
        private readonly IProductService _productService;
        private readonly IProductImageService _productImageService;
        private readonly ILogger<ProductsController> _logger;

        public ProductsController(
            IProductService productService,
            IProductImageService productImageService,
            ILogger<ProductsController> logger)
        {
            _productService = productService;
            _productImageService = productImageService;
            _logger = logger;
        }



        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<PagedResultDto<ProductDto>>> GetProducts(
            [FromQuery] int? sellerId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var searchDto = new ProductSearchDto
            {
                SellerId = sellerId,
                Page = page,
                PageSize = pageSize
            };
            var products = await _productService.SearchProductsAsync(searchDto);
            return Ok(products);
        }

        [HttpGet("featured")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetFeaturedProducts([FromQuery] int count = 8)
        {
            try
            {
                var products = await _productService.GetFeaturedProductsAsync(count);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("new")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetNewProducts([FromQuery] int count = 8)
        {
            try
            {
                var products = await _productService.GetNewProductsAsync(count);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("management")]
        public async Task<ActionResult<PagedResultDto<ProductDto>>> GetProductsForManagement(
            [FromQuery] int? sellerId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? searchTerm = null,
            [FromQuery] int[]? categoryIds = null,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] bool inStockOnly = false,
            [FromQuery] string sortBy = "CreatedAt",
            [FromQuery] bool sortDescending = true)
        {
            var searchDto = new ProductSearchDto
            {
                SellerId = sellerId,
                Page = page,
                PageSize = pageSize,
                SearchTerm = searchTerm,
                CategoryIds = categoryIds?.ToList(),
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                InStockOnly = inStockOnly,
                SortBy = sortBy,
                SortDescending = sortDescending
            };
            var products = await _productService.SearchProductsForManagementAsync(searchDto);
            return Ok(products);
        }

        [HttpGet("{id:int}/management")]
        public async Task<ActionResult<ProductDto>> GetProductForManagement(int id)
        {
            try
            {
                var product = await _productService.GetProductByIdForManagementAsync(id);
                return Ok(product);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("{id:int}")]
        [AllowAnonymous]
        public async Task<ActionResult<ProductDto>> GetProduct(int id)
        {
            try
            {
                var product = await _productService.GetProductByIdAsync(id);
                return Ok(product);
            }
            catch (Exception ex)
            {
                return NotFound(new { error = ex.Message });
            }
        }

        [HttpGet("{id:int}/related")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetRelatedProducts(int id, [FromQuery] int count = 4)
        {
            try
            {
                var products = await _productService.GetRelatedProductsAsync(id, count);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("category/{categoryId:int}")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetProductsByCategory(
            int categoryId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // Utiliser la méthode avec pagination si elle existe, sinon utiliser la méthode simple
                var products = await _productService.GetProductsByCategoryAsync(categoryId, page, pageSize);
                return Ok(products);
            }
            catch (Exception ex)
            {
                // Fallback vers la méthode simple si la méthode avec pagination n'existe pas
                try
                {
                    var productsSimple = await _productService.GetProductsByCategoryAsync(categoryId);
                    return Ok(productsSimple);
                }
                catch (Exception ex2)
                {
                    return BadRequest(new { error = ex2.Message });
                }
            }
        }

        [HttpPost]
        public async Task<ActionResult<ProductDto>> CreateProduct([FromBody] CreateProductRequest request)
        {
            try
            {
                var product = await _productService.CreateProductAsync(request);

                // Retourner directement le produit créé au lieu d'utiliser CreatedAtAction
                // car GetProduct filtre par statut d'approbation et ne peut pas trouver un produit Pending
                return Ok(product);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPut("{id:int}")]
        public async Task<ActionResult<ProductDto>> UpdateProduct(int id, UpdateProductRequest request)
        {
            try
            {
                var product = await _productService.UpdateProductAsync(id, request);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<ActionResult> DeleteProduct(int id)
        {
            try
            {
                await _productService.DeleteProductAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpPut("{id:int}/approve")]
        public async Task<ActionResult<ProductDto>> ApproveProduct(int id)
        {
            try
            {
                // Pour l'instant, on utilise un utilisateur par défaut
                // Dans une vraie application, on récupérerait l'utilisateur connecté
                var approvedBy = "admin"; // TODO: Récupérer l'utilisateur connecté

                var product = await _productService.ApproveProductAsync(id, approvedBy);
                return Ok(product);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPut("{id:int}/reject")]
        public async Task<ActionResult<ProductDto>> RejectProduct(int id, [FromBody] RejectProductRequest request)
        {
            try
            {
                // Pour l'instant, on utilise un utilisateur par défaut
                // Dans une vraie application, on récupérerait l'utilisateur connecté
                var rejectedBy = "admin"; // TODO: Récupérer l'utilisateur connecté

                var product = await _productService.RejectProductAsync(id, request.RejectionReason, rejectedBy);
                return Ok(product);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("{productId:int}/images")]
        public async Task<ActionResult<ProductDto>> AddProductImage(int productId, [FromBody] AddProductImageRequest request)
        {
            try
            {
                var product = await _productService.AddProductImageAsync(productId, new CreateProductImageRequest
                {
                    ProductId = productId,
                    Image = request.Image,
                    IsMain = request.IsMain
                });

                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("{productId:int}/images/bulk")]
        public async Task<ActionResult<IEnumerable<ProductImageDto>>> AddBulkProductImages(int productId, [FromBody] IEnumerable<CreateProductImageRequest> request)
        {
            try
            {
                var images = await _productImageService.AddBulkProductImagesAsync(productId, request);
                return Ok(images);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{productId:int}/images/{imageId:int}")]
        public async Task<ActionResult> DeleteProductImage(int productId, int imageId)
        {
            try
            {
                await _productImageService.DeleteProductImageAsync(imageId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("{id:int}/variants")]
        public async Task<ActionResult<ProductDto>> AddProductVariant(int id, CreateProductVariantRequest request)
        {
            try
            {
                var product = await _productService.AddProductVariantAsync(id, request);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPut("{productId:int}/variants/{variantId:int}")]
        public async Task<ActionResult<ProductDto>> UpdateProductVariant(
            int productId,
            int variantId,
            UpdateProductVariantRequest request)
        {
            try
            {
                var product = await _productService.UpdateProductVariantAsync(productId, variantId, request);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{productId:int}/variants/{variantId:int}")]
        public async Task<ActionResult<ProductDto>> DeleteProductVariant(int productId, int variantId)
        {
            try
            {
                var product = await _productService.DeleteProductVariantAsync(productId, variantId);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("search")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> SearchProducts(
            [FromQuery] string? searchTerm,
            [FromQuery] int? categoryId,
            [FromQuery] List<int>? categoryIds,
            [FromQuery] decimal? minPrice,
            [FromQuery] decimal? maxPrice,
            [FromQuery] string? brand,
            [FromQuery] bool? inStockOnly,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? sortBy = "newest",
            [FromQuery] bool sortDescending = true)
        {
            try
            {
                var searchDto = new ProductSearchDto
                {
                    SearchTerm = searchTerm,
                    CategoryId = categoryId,
                    CategoryIds = categoryIds,
                    MinPrice = minPrice,
                    MaxPrice = maxPrice,
                    Brand = brand,
                    IsActive = true, // Toujours retourner les produits actifs
                    Page = page,
                    PageSize = pageSize
                };

                var products = await _productService.SearchProductsAsync(searchDto);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // Endpoints pour l'inventaire
        [HttpGet("{id}/stock")]
        public async Task<ActionResult<int>> GetProductStock(int id)
        {
            try
            {
                var product = await _productService.GetProductByIdAsync(id);
                if (product == null)
                {
                    return NotFound();
                }
                return Ok(product.StockQuantity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stock for product {ProductId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("inventory-summary")]
        public async Task<ActionResult> GetInventorySummary([FromQuery] int? sellerId = null)
        {
            try
            {
                var products = await _productService.GetProductsBySeller(sellerId);

                var totalProducts = products.Count();
                var lowStockCount = products.Count(p => p.StockQuantity > 0 && p.StockQuantity <= 10);
                var outOfStockProducts = products.Count(p => p.StockQuantity == 0);
                var totalInventoryValue = products.Sum(p => p.Price * p.StockQuantity);

                var summary = new
                {
                    TotalProducts = totalProducts,
                    LowStockCount = lowStockCount,
                    OutOfStockProducts = outOfStockProducts,
                    TotalInventoryValue = totalInventoryValue
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory summary");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("low-stock")]
        public async Task<ActionResult> GetLowStockProducts([FromQuery] int? sellerId = null, [FromQuery] int threshold = 10)
        {
            try
            {
                var products = await _productService.GetProductsBySeller(sellerId);

                var lowStockProducts = products
                    .Where(p => p.StockQuantity > 0 && p.StockQuantity <= threshold)
                    .Select(p => new
                    {
                        ProductId = p.Id,
                        ProductName = p.Name,
                        SoldQuantity = 0, // Serait calculé avec les données de vente
                        Revenue = p.Price * p.StockQuantity,
                        Currency = p.Currency,
                        CurrentStock = p.StockQuantity
                    })
                    .ToList();

                return Ok(lowStockProducts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock products");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("inventory-value")]
        public async Task<ActionResult<decimal>> GetInventoryValue([FromQuery] int? sellerId = null)
        {
            try
            {
                var products = await _productService.GetProductsBySeller(sellerId);
                var totalValue = products.Sum(p => p.Price * p.StockQuantity);
                return Ok(totalValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory value");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("seller/summary")]
        public async Task<ActionResult> GetSellerProductsSummary([FromQuery] int? sellerId = null)
        {
            try
            {
                var products = await _productService.GetProductsBySeller(sellerId);

                var summary = products.Select(p => new
                {
                    Id = p.Id,
                    Name = p.Name,
                    Price = p.Price,
                    StockQuantity = p.StockQuantity,
                    Status = p.ApprovalStatus.ToString(),
                    CategoryId = p.CategoryId,
                    ImageUrl = p.Images?.FirstOrDefault()?.Url
                }).ToList();

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting seller products summary");
                return StatusCode(500, "Internal server error");
            }
        }
    }
