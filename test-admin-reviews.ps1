# Test script pour les avis admin
Write-Host "Connexion en cours..."

# Connexion
$loginData = @{
    username = "<EMAIL>"
    password = "Admin123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    $token = $loginResponse.token
    Write-Host "Connexion reussie, token obtenu"
    
    # Headers avec token
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    Write-Host "Test de l'API Reviews Admin..."
    
    # Test de l'endpoint admin reviews
    try {
        $adminReviewsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/moderation/reviews?page=1&pageSize=10" -Method GET -Headers $headers
        Write-Host "Succes API Reviews Admin"
        Write-Host "Nombre d'avis: $($adminReviewsResponse.totalCount)"
        
        if ($adminReviewsResponse.reviews -and $adminReviewsResponse.reviews.Count -gt 0) {
            $firstReview = $adminReviewsResponse.reviews[0]
            Write-Host "Premier avis:"
            Write-Host "  ID: $($firstReview.id)"
            Write-Host "  Produit: $($firstReview.productName)"
            Write-Host "  Image: $($firstReview.productImageUrl)"
            Write-Host "  Vendeur: $($firstReview.sellerName)"
        }
    }
    catch {
        Write-Host "Erreur API Reviews Admin: $($_.Exception.Message)"
        Write-Host "Code de statut: $($_.Exception.Response.StatusCode)"
    }
}
catch {
    Write-Host "Erreur de connexion: $($_.Exception.Message)"
}
