@page "/catalog/products/{id:int}"
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Models.Cart
@using NafaPlace.Reviews.DTOs
@using NafaPlace.Web.Services
@using NafaPlace.Web.Components.Reviews
@using System.Security.Claims
@inject IProductService ProductService
@inject IReviewService ReviewService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject ICartService CartService
@inject AuthenticationStateProvider AuthenticationStateProvider

@if (_product == null)
{
    <p><em>Chargement du produit...</em></p>
}
else
{
    <div class="container my-5">
        <div class="row">
            <div class="col-md-6">
                <img src="@(_product.Images.Any() ? _product.Images.First().Url : "/images/placeholder.png")" class="img-fluid" alt="@_product.Name">
            </div>
            <div class="col-md-6">
                <h2>@_product.Name</h2>
                <p class="text-muted">@_product.Category?.Name</p>
                <h3 class="mb-3">@((_selectedVariant?.Price ?? _product.Price).ToString("C"))</h3>
                <p>@_product.Description</p>

                <hr>

                <!-- Variantes -->
                @if (_product.Variants != null && _product.Variants.Any())
                {
                    <h5>Variantes</h5>
                    <div class="mb-3">
                        @foreach (var variant in _product.Variants)
                        {
                            <button class="btn @(_selectedVariant?.Id == variant.Id ? "btn-primary" : "btn-outline-primary") me-2" @onclick="() => SelectVariant(variant)">
                                @variant.Name
                            </button>
                        }
                    </div>
                }

                <div class="d-flex mt-4">
                    <input type="number" class="form-control me-3" style="width: 80px;" @bind="_quantity" min="1">
                    <button class="btn btn-primary flex-grow-1" @onclick="AddToCart">
                        <i class="fas fa-shopping-cart me-2"></i> Ajouter au panier
                    </button>
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4">Avis clients</h3>



                <!-- Review Summary -->
                <ReviewSummary Summary="_reviewSummary"
                              ShowWriteReviewButton="@_canWriteReview"
                              IsEditMode="@(_existingUserReview != null)"
                              OnWriteReviewClick="ShowReviewForm" />

                <!-- Review Form -->
                @if (_showReviewForm)
                {
                    <div class="mb-4">
                        <ReviewForm ProductId="Id"
                                   ExistingReview="@_editingReview"
                                   IsEdit="@(_editingReview != null)"
                                   ShowVerifiedPurchase="false"
                                   OnSubmit="HandleCreateReview"
                                   OnUpdate="HandleUpdateReview"
                                   OnCancel="HideReviewForm" />
                    </div>
                }

                <!-- Reviews List -->
                <ReviewsList Reviews="_reviews"
                            ShowActions="true"
                            ShowPagination="true"
                            CurrentPage="_currentPage"
                            TotalPages="_totalPages"
                            CurrentUserId="@_currentUserId"
                            OnEditReview="EditReview"
                            OnDeleteReview="DeleteReview"
                            OnPageChanged="LoadReviews"
                            OnToggleHelpful="ToggleHelpful" />
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public int Id { get; set; }

    private ProductDto? _product;
    private ProductVariantDto? _selectedVariant;
    private int _quantity = 1;

    // Reviews related fields
    private ReviewSummaryDto? _reviewSummary;
    private List<ReviewDto> _reviews = new();
    private bool _showReviewForm = false;
    private ReviewDto? _editingReview = null;
    private bool _canWriteReview = false;
    private string? _currentUserId;
    private string? _currentUserName;
    private ReviewDto? _existingUserReview;
    private int _currentPage = 1;
    private int _totalPages = 1;
    private const int _pageSize = 10;



    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        _product = await ProductService.GetProductByIdAsync(Id);
        if (_product?.Variants?.Any() == true)
        {
            _selectedVariant = _product.Variants.First();
        }

        // Load user info
        var authState = await AuthenticationStateTask;
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            _currentUserId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            _currentUserName = authState.User.FindFirst(ClaimTypes.Name)?.Value ?? authState.User.FindFirst(ClaimTypes.Email)?.Value;

            if (!string.IsNullOrEmpty(_currentUserId))
            {
                _canWriteReview = await ReviewService.CanUserReviewProductAsync(Id, _currentUserId);

                // Si l'utilisateur ne peut pas écrire d'avis, vérifier s'il a un avis existant à modifier
                if (!_canWriteReview)
                {
                    var userReviews = await ReviewService.GetUserReviewsAsync(_currentUserId, 1, 100);
                    _existingUserReview = userReviews.Reviews.FirstOrDefault(r => r.ProductId == Id);
                    _canWriteReview = true; // Permettre la modification
                }
            }
        }

        // Load reviews data
        await LoadReviewsData();
    }

    private void SelectVariant(ProductVariantDto variant)
    {
        _selectedVariant = variant;
    }

    private async Task AddToCart()
    {
        var item = new CartItemCreateDto
        {
            ProductId = _product.Id,
            ProductName = _product.Name,
            Price = _selectedVariant?.Price ?? _product.Price,
            Quantity = _quantity,
            VariantId = _selectedVariant?.Id,
            VariantName = _selectedVariant?.Name
        };

        var authState = await AuthenticationStateTask;
        var user = authState.User;
        string userId;

        if (user.Identity.IsAuthenticated)
        {
            userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? "";
        }
        else
        {
            // Utiliser un ID de session temporaire pour les utilisateurs non connectés
            userId = await GetOrCreateGuestUserId();
        }

        await CartService.AddItemToCartAsync(userId, item);
        NavigationManager.NavigateTo("/cart");
    }

    private async Task LoadReviewsData()
    {
        try
        {
            // Load review summary
            _reviewSummary = await ReviewService.GetReviewSummaryAsync(Id);

            // Load reviews
            var reviewsResult = await ReviewService.GetReviewsByProductIdAsync(Id, _currentPage, _pageSize);
            _reviews = reviewsResult.Reviews;
            _totalPages = reviewsResult.TotalPages;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des reviews: {ex.Message}");
        }
    }

    private async Task LoadReviews(int page)
    {
        _currentPage = page;
        await LoadReviewsData();
        StateHasChanged();
    }

    private void ShowReviewForm()
    {
        if (!string.IsNullOrEmpty(_currentUserId))
        {
            _showReviewForm = true;
            _editingReview = _existingUserReview; // Pré-remplir avec l'avis existant s'il y en a un
            StateHasChanged();
        }
        else
        {
            NavigationManager.NavigateTo("/auth/login");
        }
    }

    private void HideReviewForm()
    {
        _showReviewForm = false;
        _editingReview = null;
        StateHasChanged();
    }

    private async Task HandleCreateReview(CreateReviewRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentUserId) || string.IsNullOrEmpty(_currentUserName))
            {
                NavigationManager.NavigateTo("/auth/login");
                return;
            }

            request.UserId = _currentUserId;
            request.UserName = _currentUserName;

            await ReviewService.CreateReviewAsync(request);

            HideReviewForm();
            await LoadReviewsData();
            _canWriteReview = false; // User can only write one review per product
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création de la review: {ex.Message}");
            // TODO: Show error message to user
        }
    }

    private async Task HandleUpdateReview(UpdateReviewRequest request)
    {
        try
        {
            if (_editingReview == null) return;

            await ReviewService.UpdateReviewAsync(_editingReview.Id, request);

            HideReviewForm();
            await LoadReviewsData();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour de la review: {ex.Message}");
            // TODO: Show error message to user
        }
    }

    private void EditReview(ReviewDto review)
    {
        _editingReview = review;
        _showReviewForm = true;
        StateHasChanged();
    }

    private async Task DeleteReview(ReviewDto review)
    {
        try
        {
            await ReviewService.DeleteReviewAsync(review.Id);
            await LoadReviewsData();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de la review: {ex.Message}");
            // TODO: Show error message to user
        }
    }

    private async Task ToggleHelpful(ReviewDto review)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentUserId))
            {
                NavigationManager.NavigateTo("/auth/login");
                return;
            }

            // TODO: Track if user already marked this review as helpful
            await ReviewService.MarkReviewHelpfulAsync(review.Id);
            await LoadReviewsData();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du marquage de la review comme utile: {ex.Message}");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Guid.NewGuid():N}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }
}
