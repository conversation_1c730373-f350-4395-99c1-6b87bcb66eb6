# Test avec un utilisateur existant
Write-Host "Test avec utilisateur existant" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost"
$identityPort = "5155"
$reviewsPort = "5006"
$wishlistPort = "5008"
$catalogPort = "5243"

Write-Host "`n1. Connexion avec utilisateur existant..." -ForegroundColor Cyan

# Test avec différents utilisateurs existants
$users = @(
    @{ identifier = "lamine92"; password = "Test123!" },
    @{ identifier = "admin"; password = "Admin123!" },
    @{ identifier = "seller1"; password = "Seller123!" }
)

$successfulLogin = $false
$token = ""
$userId = ""
$username = ""

foreach ($user in $users) {
    Write-Host "   Tentative avec: $($user.identifier)" -ForegroundColor Gray
    
    $loginData = @{
        identifier = $user.identifier
        password = $user.password
    } | ConvertTo-Json
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl`:$identityPort/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
        
        if ($loginResponse.accessToken) {
            $token = $loginResponse.accessToken
            $userId = $loginResponse.user.id
            $username = $loginResponse.user.username
            $successfulLogin = $true
            
            Write-Host "✅ Connexion reussie avec: $username" -ForegroundColor Green
            Write-Host "   User ID: $userId" -ForegroundColor Gray
            Write-Host "   Token: $($token.Substring(0, 50))..." -ForegroundColor Gray
            break
        }
    }
    catch {
        Write-Host "   ❌ Echec avec: $($user.identifier)" -ForegroundColor Red
    }
}

if ($successfulLogin) {
    # Headers d'authentification
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    Write-Host "`n2. Test des produits..." -ForegroundColor Cyan
    
    try {
        $products = Invoke-RestMethod -Uri "$baseUrl`:$catalogPort/api/products" -Method GET
        
        if ($products -and $products.Count -gt 0) {
            $productId = $products[0].id
            $productName = $products[0].name
            
            Write-Host "✅ Produits trouves !" -ForegroundColor Green
            Write-Host "   Test avec: $productName (ID: $productId)" -ForegroundColor Gray
            
            Write-Host "`n3. Test creation d'avis..." -ForegroundColor Cyan
            
            # Données pour créer un avis
            $reviewData = @{
                productId = $productId
                userId = $userId.ToString()
                userName = $username
                rating = 4
                title = "Test automatique - Corrections JWT"
                comment = "Test automatique pour verifier que les corrections JWT fonctionnent correctement."
                isVerifiedPurchase = $false
            } | ConvertTo-Json
            
            try {
                Write-Host "   Envoi de l'avis..." -ForegroundColor Gray
                $reviewResponse = Invoke-RestMethod -Uri "$baseUrl`:$reviewsPort/api/reviews" -Method POST -Body $reviewData -Headers $headers
                
                Write-Host "✅ Avis cree avec succes !" -ForegroundColor Green
                Write-Host "   Review ID: $($reviewResponse.id)" -ForegroundColor Gray
                Write-Host "   Titre: $($reviewResponse.title)" -ForegroundColor Gray
                Write-Host "   Note: $($reviewResponse.rating)/5" -ForegroundColor Gray
                Write-Host "   Statut: $($reviewResponse.status)" -ForegroundColor Gray
                
            }
            catch {
                Write-Host "❌ Erreur creation avis: $($_.Exception.Message)" -ForegroundColor Red
                if ($_.Exception.Response) {
                    $statusCode = $_.Exception.Response.StatusCode
                    Write-Host "   Code de statut: $statusCode" -ForegroundColor Red
                }
            }
            
            Write-Host "`n4. Test wishlist..." -ForegroundColor Cyan
            
            try {
                # Test du compteur wishlist
                $wishlistCount = Invoke-RestMethod -Uri "$baseUrl`:$wishlistPort/api/wishlist/count" -Method GET -Headers $headers
                Write-Host "✅ Wishlist accessible ! Nombre: $wishlistCount" -ForegroundColor Green
                
                # Test d'ajout à la wishlist
                $wishlistAddData = @{
                    productId = $productId
                } | ConvertTo-Json
                
                try {
                    $addResponse = Invoke-RestMethod -Uri "$baseUrl`:$wishlistPort/api/wishlist/add" -Method POST -Body $wishlistAddData -Headers $headers
                    Write-Host "✅ Produit ajoute a la wishlist !" -ForegroundColor Green
                }
                catch {
                    Write-Host "⚠️ Produit deja dans wishlist ou erreur" -ForegroundColor Yellow
                }
                
            }
            catch {
                Write-Host "❌ Erreur wishlist: $($_.Exception.Message)" -ForegroundColor Red
                if ($_.Exception.Response) {
                    $statusCode = $_.Exception.Response.StatusCode
                    Write-Host "   Code de statut: $statusCode" -ForegroundColor Red
                }
            }
            
        }
        else {
            Write-Host "❌ Aucun produit trouve" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Erreur produits: $($_.Exception.Message)" -ForegroundColor Red
    }
    
}
else {
    Write-Host "`n❌ Aucune connexion reussie" -ForegroundColor Red
}

Write-Host "`n🏁 Test termine !" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

if ($successfulLogin) {
    Write-Host "`n✅ SUCCES: Les corrections JWT semblent fonctionner !" -ForegroundColor Green
    Write-Host "Vous pouvez maintenant tester manuellement sur le site web." -ForegroundColor Green
}
else {
    Write-Host "`n❌ ECHEC: Probleme d'authentification detecte" -ForegroundColor Red
    Write-Host "Verifiez les logs des services pour plus de details." -ForegroundColor Red
}
