-- Vérifier les 10 dernières commandes et leurs utilisateurs
SELECT o."Id", o."UserId", o."OrderDate", o."TotalAmount", o."Status", 
       oi."ProductId", oi."SellerId", oi."Quantity", oi."UnitPrice"
FROM "Orders" o
JOIN "OrderItems" oi ON o."Id" = oi."OrderId"
ORDER BY o."OrderDate" DESC
LIMIT 10;

-- Vérifier spécifiquement les commandes #30, #32, #33
SELECT o."Id", o."UserId", o."OrderDate", o."TotalAmount", o."Status", 
       oi."ProductId", oi."SellerId", oi."Quantity", oi."UnitPrice"
FROM "Orders" o
JOIN "OrderItems" oi ON o."Id" = oi."OrderId"
WHERE o."Id" IN (30, 32, 33)
ORDER BY o."Id";

-- Compter les commandes par utilisateur
SELECT "UserId", COUNT(*) as "NombreCommandes"
FROM "Orders"
GROUP BY "UserId"
ORDER BY COUNT(*) DESC;
