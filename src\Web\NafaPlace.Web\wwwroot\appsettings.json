{"ApiEndpoints": {"IdentityApi": "http://localhost:5155", "CatalogApi": "http://localhost:5243", "CartApi": "http://localhost:5002", "OrderApi": "http://localhost:5003", "PaymentApi": "http://localhost:5004", "ReviewApi": "http://localhost:5005", "NotificationsApi": "http://localhost:5006", "WishlistApi": "http://localhost:5007", "CouponsApi": "http://localhost:5009/api/coupon"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Cloudinary": {"CloudName": "${CLOUDINARY_CLOUD_NAME}", "ApiKey": "${CLOUDINARY_API_KEY}", "ApiSecret": "${CLOUDINARY_API_SECRET}"}, "Stripe": {"PublishableKey": "${STRIPE_PUBLISHABLE_KEY}", "SecretKey": "${STRIPE_SECRET_KEY}"}}