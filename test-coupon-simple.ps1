# Test simple pour l'application de coupons
Write-Host "Test d'application de coupon..." -ForegroundColor Cyan

# 1. Vérifier que l'API Coupon fonctionne
Write-Host "1. Test de l'API Coupon..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5009/api/coupon" -Method GET
    Write-Host "API Coupon accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "Erreur API Coupon: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Vérifier que l'API Wishlist fonctionne maintenant
Write-Host "2. Test de l'API Wishlist..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5008/api/wishlist" -Method GET
    Write-Host "API Wishlist accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "Erreur API Wishlist: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Vérifier les coupons créés
Write-Host "3. Vérification des coupons créés..." -ForegroundColor Yellow
try {
    $coupons = docker-compose exec coupon-db psql -U postgres -d "NafaPlace.Coupon" -t -c "SELECT \"Code\", \"Name\" FROM \"Coupons\" WHERE \"IsActive\" = true;"
    Write-Host "Coupons disponibles:" -ForegroundColor Green
    Write-Host $coupons -ForegroundColor White
} catch {
    Write-Host "Erreur lors de la verification des coupons: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nResume des corrections:" -ForegroundColor Cyan
Write-Host "Tables Wishlist creees manuellement" -ForegroundColor Green
Write-Host "Coupons de test ajoutes (BIENVENUE, REDUCTION20, LIVRAISON)" -ForegroundColor Green
Write-Host "Erreurs 500 de l'API Wishlist corrigees" -ForegroundColor Green

Write-Host "`nInstructions pour tester les coupons:" -ForegroundColor Yellow
Write-Host "1. Allez sur http://localhost:8080/cart" -ForegroundColor White
Write-Host "2. Ajoutez des produits au panier" -ForegroundColor White
Write-Host "3. Essayez d'appliquer un de ces codes:" -ForegroundColor White
Write-Host "   - BIENVENUE (10% de reduction)" -ForegroundColor Cyan
Write-Host "   - REDUCTION20 (20% de reduction)" -ForegroundColor Cyan
Write-Host "   - LIVRAISON (livraison gratuite)" -ForegroundColor Cyan
