# Test de debug pour l'authentification
Write-Host "Debug de l'authentification" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green

$baseUrl = "http://localhost:5155"

# Test avec différents mots de passe pour lamine92
$passwords = @("Test123!", "test123", "password", "lamine123", "Password123!")

Write-Host "`nUtilisateurs dans la DB:" -ForegroundColor Cyan
Write-Host "- admin (<EMAIL>)" -ForegroundColor Gray
Write-Host "- lamine92 (<EMAIL>)" -ForegroundColor Gray
Write-Host "- moussa92 (<EMAIL>)" -ForegroundColor Gray
Write-Host "- user (<EMAIL>)" -ForegroundColor Gray

Write-Host "`nTest de connexion avec lamine92..." -ForegroundColor <PERSON>an

foreach ($password in $passwords) {
    Write-Host "`n   Tentative avec mot de passe: $password" -ForegroundColor Gray
    
    $loginData = @{
        identifier = "lamine92"
        password = $password
    } | ConvertTo-Json
    
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json" -UseBasicParsing
        
        if ($response.StatusCode -eq 200) {
            $loginResponse = $response.Content | ConvertFrom-Json
            
            if ($loginResponse.accessToken) {
                Write-Host "✅ SUCCES avec mot de passe: $password" -ForegroundColor Green
                Write-Host "   Token: $($loginResponse.accessToken.Substring(0, 50))..." -ForegroundColor Gray
                Write-Host "   User ID: $($loginResponse.user.id)" -ForegroundColor Gray
                
                # Test immédiat de l'API Reviews avec ce token
                Write-Host "`n   Test immediat de l'API Reviews..." -ForegroundColor Cyan
                
                $headers = @{
                    "Authorization" = "Bearer $($loginResponse.accessToken)"
                    "Content-Type" = "application/json"
                }
                
                $reviewData = @{
                    productId = 1
                    userId = $loginResponse.user.id.ToString()
                    userName = "lamine92"
                    rating = 5
                    title = "Test debug - Mot de passe trouve"
                    comment = "Test automatique avec le bon mot de passe: $password"
                    isVerifiedPurchase = $false
                } | ConvertTo-Json
                
                try {
                    $reviewResponse = Invoke-RestMethod -Uri "http://localhost:5006/api/reviews" -Method POST -Body $reviewData -Headers $headers
                    Write-Host "   ✅ Avis cree avec succes !" -ForegroundColor Green
                    Write-Host "   Review ID: $($reviewResponse.id)" -ForegroundColor Gray
                }
                catch {
                    Write-Host "   ❌ Erreur creation avis: $($_.Exception.Message)" -ForegroundColor Red
                }
                
                break
            }
        }
    }
    catch {
        $statusCode = "Unknown"
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
        }
        Write-Host "   ❌ Echec ($statusCode): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nTest avec d'autres utilisateurs..." -ForegroundColor Cyan

# Test avec d'autres utilisateurs et mots de passe communs
$otherUsers = @(
    @{ identifier = "admin"; passwords = @("admin", "Admin123!", "password", "admin123") },
    @{ identifier = "user"; passwords = @("user", "User123!", "password", "user123") }
)

foreach ($userTest in $otherUsers) {
    Write-Host "`n   Test avec utilisateur: $($userTest.identifier)" -ForegroundColor Gray
    
    foreach ($password in $userTest.passwords) {
        $loginData = @{
            identifier = $userTest.identifier
            password = $password
        } | ConvertTo-Json
        
        try {
            $response = Invoke-WebRequest -Uri "$baseUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json" -UseBasicParsing
            
            if ($response.StatusCode -eq 200) {
                Write-Host "     ✅ SUCCES: $($userTest.identifier) / $password" -ForegroundColor Green
                break
            }
        }
        catch {
            # Silencieux pour ne pas encombrer
        }
    }
}

Write-Host "`n🏁 Test de debug termine" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
