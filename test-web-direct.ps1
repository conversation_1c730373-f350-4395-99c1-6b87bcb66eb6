# Test direct via l'interface web
Write-Host "Test Direct via Interface Web" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

Write-Host "`nOuverture du navigateur..." -ForegroundColor Cyan
Write-Host "URL: http://localhost:8080" -ForegroundColor Gray

# Ouvrir le navigateur
Start-Process "http://localhost:8080"

Write-Host "`n📋 Instructions de test manuel:" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

Write-Host "`n1. 👤 CONNEXION:" -ForegroundColor Cyan
Write-Host "   Cliquez sur 'Se connecter' ou 'Login'" -ForegroundColor White
Write-Host "   Essayez ces utilisateurs:" -ForegroundColor White
Write-Host "   - Username: testuser20250718191441" -ForegroundColor Gray
Write-Host "     Password: Test123!" -ForegroundColor Gray
Write-Host "   - Username: lamine92" -ForegroundColor Gray
Write-Host "     Password: [essayez différents mots de passe]" -ForegroundColor Gray

Write-Host "`n2. 📦 NAVIGATION:" -ForegroundColor Cyan
Write-Host "   Une fois connecté, allez sur une page produit" -ForegroundColor White
Write-Host "   Cherchez la section 'Avis' ou 'Reviews'" -ForegroundColor White

Write-Host "`n3. ⭐ TEST AVIS:" -ForegroundColor Cyan
Write-Host "   Essayez de créer un avis/commentaire:" -ForegroundColor White
Write-Host "   - Note: 5 étoiles" -ForegroundColor Gray
Write-Host "   - Titre: Test des corrections JWT" -ForegroundColor Gray
Write-Host "   - Commentaire: Test pour vérifier les corrections" -ForegroundColor Gray

Write-Host "`n4. 🔍 VÉRIFICATION:" -ForegroundColor Cyan
Write-Host "   Regardez dans la console du navigateur (F12)" -ForegroundColor White
Write-Host "   Cherchez les erreurs:" -ForegroundColor White
Write-Host "   - ✅ Si pas d'erreur 401: Corrections JWT OK" -ForegroundColor Green
Write-Host "   - ❌ Si erreur 401: Problème JWT persiste" -ForegroundColor Red

Write-Host "`n5. 💝 TEST WISHLIST:" -ForegroundColor Cyan
Write-Host "   Essayez d'ajouter un produit à la wishlist" -ForegroundColor White
Write-Host "   Vérifiez le compteur de wishlist" -ForegroundColor White

Write-Host "`n🔧 DÉPANNAGE:" -ForegroundColor Yellow
Write-Host "=============" -ForegroundColor Yellow
Write-Host "Si la connexion ne fonctionne pas:" -ForegroundColor White
Write-Host "1. Essayez de créer un nouveau compte" -ForegroundColor Gray
Write-Host "2. Utilisez un email unique" -ForegroundColor Gray
Write-Host "3. Mot de passe: au moins 8 caractères avec majuscule et chiffre" -ForegroundColor Gray

Write-Host "`n📊 RÉSULTATS ATTENDUS:" -ForegroundColor Yellow
Write-Host "======================" -ForegroundColor Yellow
Write-Host "✅ SUCCÈS si:" -ForegroundColor Green
Write-Host "   - Connexion fonctionne" -ForegroundColor White
Write-Host "   - Création d'avis fonctionne sans erreur 401" -ForegroundColor White
Write-Host "   - Wishlist fonctionne" -ForegroundColor White

Write-Host "❌ ÉCHEC si:" -ForegroundColor Red
Write-Host "   - Erreurs 401 (Unauthorized) dans la console" -ForegroundColor White
Write-Host "   - Impossible de créer des avis" -ForegroundColor White
Write-Host "   - Wishlist ne fonctionne pas" -ForegroundColor White

Write-Host "`n⏳ En attente de vos tests..." -ForegroundColor Cyan
Write-Host "Appuyez sur Entrée quand vous avez terminé les tests" -ForegroundColor Gray
Read-Host

Write-Host "`n📝 Veuillez rapporter les résultats:" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Yellow
Write-Host "1. La connexion a-t-elle fonctionné ? (Oui/Non)" -ForegroundColor White
Write-Host "2. Avez-vous pu créer un avis ? (Oui/Non)" -ForegroundColor White
Write-Host "3. Y a-t-il des erreurs 401 dans la console ? (Oui/Non)" -ForegroundColor White
Write-Host "4. La wishlist fonctionne-t-elle ? (Oui/Non)" -ForegroundColor White

Write-Host "`n🏁 Test manuel terminé" -ForegroundColor Green
