using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using NafaPlace.SellerPortal;
using NafaPlace.SellerPortal.Services;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Http;
using Microsoft.Extensions.DependencyInjection;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Récupération des paramètres de configuration
var identityApiUrl = builder.Configuration.GetValue<string>("ApiSettings:IdentityApiUrl") ?? "http://localhost:5155";
var catalogApiUrl = builder.Configuration.GetValue<string>("ApiSettings:CatalogApiUrl") ?? "http://localhost:5243";
var reviewApiUrl = builder.Configuration.GetValue<string>("ApiSettings:ReviewApiUrl") ?? "http://localhost:5006";
var inventoryApiUrl = builder.Configuration.GetValue<string>("ApiSettings:InventoryApiUrl") ?? "http://localhost:5244";
var orderApiUrl = builder.Configuration.GetValue<string>("ApiSettings:OrderApiUrl") ?? "http://localhost:5004";

// Configuration des HttpClient pour les différentes API
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });

// HttpClient pour l'API Identity
builder.Services.AddHttpClient("IdentityApi", client =>
{
    client.BaseAddress = new Uri(identityApiUrl);
});

// HttpClient pour l'API Catalog
builder.Services.AddHttpClient("CatalogApi", client =>
{
    client.BaseAddress = new Uri(catalogApiUrl);
});

// HttpClient pour l'API Reviews
builder.Services.AddHttpClient("ReviewApi", client =>
{
    client.BaseAddress = new Uri(reviewApiUrl);
});

// HttpClient pour l'API Inventory
builder.Services.AddHttpClient("InventoryApi", client =>
{
    client.BaseAddress = new Uri(inventoryApiUrl);
});

// HttpClient pour l'API Order
builder.Services.AddHttpClient("OrderApi", client =>
{
    client.BaseAddress = new Uri(orderApiUrl);
});

// Services existants
builder.Services.AddScoped<ProductService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var logger = sp.GetRequiredService<ILogger<ProductService>>();
    var configuration = sp.GetRequiredService<IConfiguration>();
    return new ProductService(httpClientFactory, logger, configuration);
});
builder.Services.AddScoped<NotificationService>();
builder.Services.AddScoped<ImageService>();

// Services pour les commandes et statistiques
builder.Services.AddScoped<IOrderService, OrderService>();
builder.Services.AddScoped<IStatisticsService, StatisticsService>();

// Service pour les reviews
builder.Services.AddScoped<IReviewService, ReviewService>();

// Service pour l'inventaire
builder.Services.AddScoped<InventoryService>();

// Service pour les coupons
builder.Services.AddScoped<CouponService>(sp =>
{
    var httpClient = new HttpClient();
    var configuration = sp.GetRequiredService<IConfiguration>();
    var logger = sp.GetRequiredService<ILogger<CouponService>>();
    return new CouponService(httpClient, configuration, logger);
});

// Services d'authentification
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddScoped<AuthenticationStateProvider, ApiAuthenticationStateProvider>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddAuthorizationCore();

Console.WriteLine($"Identity API URL: {identityApiUrl}");
Console.WriteLine($"Catalog API URL: {catalogApiUrl}");

await builder.Build().RunAsync();
