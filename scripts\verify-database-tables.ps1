# Script de vérification des tables de base de données NafaPlace
# Ce script vérifie que toutes les tables attendues sont créées dans les bases de données

param(
    [string]$BaseHost = "localhost",
    [string]$Username = "postgres",
    [string]$Password = "NafaPlace2025@Dev"
)

Write-Host "🔍 Vérification des tables de base de données NafaPlace..." -ForegroundColor Cyan
Write-Host "📊 Host: $BaseHost, User: $Username" -ForegroundColor Gray

# Configuration des bases de données
$databases = @{
    "Identity" = @{ Port = 5433; Database = "NafaPlace.Identity" }
    "Catalog" = @{ Port = 5432; Database = "NafaPlace.Catalog" }
    "Order" = @{ Port = 5434; Database = "NafaPlace.Order" }
    "Reviews" = @{ Port = 5435; Database = "NafaPlace.Reviews" }
    "Notifications" = @{ Port = 5436; Database = "NafaPlace.Notifications" }
    "Wishlist" = @{ Port = 5437; Database = "NafaPlace.Wishlist" }
    "Inventory" = @{ Port = 5438; Database = "NafaPlace.Inventory" }
    "Coupon" = @{ Port = 5439; Database = "NafaPlace.Coupon" }
    "Delivery" = @{ Port = 5440; Database = "NafaPlace.Delivery" }
}

# Fonction pour exécuter une requête SQL
function Invoke-SqlQuery {
    param(
        [string]$Query,
        [string]$Host,
        [int]$Port,
        [string]$Database,
        [string]$Username,
        [string]$Password
    )

    try {
        # Construire la chaîne de connexion
        $connectionString = "postgresql://$Username`:$Password@$Host`:$Port/$Database"

        # Utiliser psql pour exécuter la requête
        $result = & psql $connectionString -t -c $Query 2>&1
        if ($LASTEXITCODE -eq 0) {
            return $result
        } else {
            return $null
        }
    }
    catch {
        return $null
    }
}

# Liste des tables attendues par service
$expectedTables = @{
    "Identity" = @(
        "Users",
        "Roles",
        "UserRoles",
        "RefreshTokens",
        "__EFMigrationsHistory"
    )
    "Catalog" = @(
        "Categories",
        "Products",
        "ProductImages",
        "ProductVariants",
        "ProductAttributes",
        "ProductVariantAttributes",
        "Sellers",
        "__EFMigrationsHistory"
    )
    "Order" = @(
        "Orders",
        "OrderItems",
        "__EFMigrationsHistory"
    )
    "Inventory" = @(
        "StockReservations",
        "StockAlerts",
        "StockAlertNotifications",
        "StockMovements",
        "__EFMigrationsHistory"
    )
    "Notifications" = @(
        "Notifications",
        "NotificationTemplates",
        "__EFMigrationsHistory"
    )
    "Delivery" = @(
        "DeliveryZones",
        "Carriers",
        "DeliveryOrders",
        "DeliveryTrackings",
        "__EFMigrationsHistory"
    )
    "Coupon" = @(
        "Coupons",
        "__EFMigrationsHistory"
    )
    "Wishlist" = @(
        "WishlistItems",
        "__EFMigrationsHistory"
    )
    "Reviews" = @(
        "Reviews",
        "__EFMigrationsHistory"
    )
}

Write-Host "`n📋 Tables attendues par service:" -ForegroundColor Yellow
foreach ($service in $expectedTables.Keys) {
    Write-Host "  🔹 $service : $($expectedTables[$service].Count) tables" -ForegroundColor Gray
}

# Vérifier la connexion à chaque base de données
Write-Host "`n🔌 Test de connexion aux bases de données..." -ForegroundColor Cyan
$connectionResults = @{}
foreach ($service in $databases.Keys) {
    $dbConfig = $databases[$service]
    $connectionTest = Invoke-SqlQuery "SELECT 1;" $BaseHost $dbConfig.Port $dbConfig.Database $Username $Password
    $connectionResults[$service] = ($null -ne $connectionTest)

    if ($connectionResults[$service]) {
        Write-Host "  ✅ $service ($($dbConfig.Database):$($dbConfig.Port))" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $service ($($dbConfig.Database):$($dbConfig.Port))" -ForegroundColor Red
    }
}

$connectedDatabases = ($connectionResults.Values | Where-Object { $_ }).Count
Write-Host "`n📊 Bases de données connectées: $connectedDatabases/$($databases.Count)" -ForegroundColor $(if ($connectedDatabases -eq $databases.Count) { "Green" } else { "Yellow" })

# Récupérer la liste des tables pour chaque base de données
Write-Host "`n📊 Récupération des tables par base de données..." -ForegroundColor Cyan
$existingTablesQuery = @"
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
ORDER BY table_name;
"@

$allExistingTables = @{}
foreach ($service in $databases.Keys) {
    if ($connectionResults[$service]) {
        $dbConfig = $databases[$service]
        $existingTablesResult = Invoke-SqlQuery $existingTablesQuery $BaseHost $dbConfig.Port $dbConfig.Database $Username $Password

        if ($null -ne $existingTablesResult) {
            $existingTables = $existingTablesResult | Where-Object { $_.Trim() -ne "" } | ForEach-Object { $_.Trim() }
            $allExistingTables[$service] = $existingTables
            Write-Host "  📋 $service : $($existingTables.Count) tables trouvées" -ForegroundColor Gray
        } else {
            $allExistingTables[$service] = @()
            Write-Host "  ❌ $service : Impossible de récupérer les tables" -ForegroundColor Red
        }
    } else {
        $allExistingTables[$service] = @()
        Write-Host "  ❌ $service : Base de données non accessible" -ForegroundColor Red
    }
}

# Vérifier chaque service
$totalExpected = 0
$totalFound = 0
$missingTables = @()
$extraTables = @()

Write-Host "`n🔍 Vérification par service:" -ForegroundColor Cyan

foreach ($service in $expectedTables.Keys) {
    Write-Host "`n🔹 Service: $service" -ForegroundColor Yellow

    $serviceExpected = $expectedTables[$service]
    $serviceExisting = $allExistingTables[$service]
    $serviceFound = 0
    $serviceMissing = @()

    if ($connectionResults[$service]) {
        foreach ($table in $serviceExpected) {
            $totalExpected++
            if ($serviceExisting -contains $table) {
                Write-Host "  ✅ $table" -ForegroundColor Green
                $serviceFound++
                $totalFound++
            } else {
                Write-Host "  ❌ $table (MANQUANTE)" -ForegroundColor Red
                $serviceMissing += $table
                $missingTables += "$service.$table"
            }
        }

        # Identifier les tables supplémentaires dans ce service
        foreach ($table in $serviceExisting) {
            if ($serviceExpected -notcontains $table) {
                $extraTables += "$service.$table"
                Write-Host "  ➕ $table (supplémentaire)" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "  ❌ Base de données non accessible" -ForegroundColor Red
        foreach ($table in $serviceExpected) {
            $totalExpected++
            $missingTables += "$service.$table"
        }
    }

    $servicePercentage = if ($serviceExpected.Count -gt 0) { [math]::Round(($serviceFound / $serviceExpected.Count) * 100, 1) } else { 0 }
    Write-Host "  📊 $serviceFound/$($serviceExpected.Count) tables trouvées ($servicePercentage%)" -ForegroundColor $(if ($serviceFound -eq $serviceExpected.Count) { "Green" } else { "Yellow" })

    if ($serviceMissing.Count -gt 0) {
        Write-Host "  ⚠️  Tables manquantes: $($serviceMissing -join ', ')" -ForegroundColor Red
    }
}

# Les tables supplémentaires ont déjà été identifiées dans la boucle précédente

# Résumé final
Write-Host "`n📊 RÉSUMÉ FINAL" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

$overallPercentage = if ($totalExpected -gt 0) { [math]::Round(($totalFound / $totalExpected) * 100, 1) } else { 0 }
Write-Host "📋 Tables attendues: $totalExpected" -ForegroundColor Gray
Write-Host "✅ Tables trouvées: $totalFound" -ForegroundColor $(if ($totalFound -eq $totalExpected) { "Green" } else { "Yellow" })
Write-Host "❌ Tables manquantes: $($missingTables.Count)" -ForegroundColor $(if ($missingTables.Count -eq 0) { "Green" } else { "Red" })
Write-Host "➕ Tables supplémentaires: $($extraTables.Count)" -ForegroundColor $(if ($extraTables.Count -eq 0) { "Green" } else { "Yellow" })
Write-Host "📊 Pourcentage de complétude: $overallPercentage%" -ForegroundColor $(if ($overallPercentage -eq 100) { "Green" } else { "Yellow" })

if ($missingTables.Count -gt 0) {
    Write-Host "`n❌ TABLES MANQUANTES:" -ForegroundColor Red
    foreach ($table in $missingTables) {
        Write-Host "  - $table" -ForegroundColor Red
    }
}

if ($extraTables.Count -gt 0) {
    Write-Host "`n➕ TABLES SUPPLÉMENTAIRES (non attendues):" -ForegroundColor Yellow
    foreach ($table in $extraTables) {
        Write-Host "  - $table" -ForegroundColor Yellow
    }
}

# Vérifier les migrations pour chaque base de données
Write-Host "`n🔄 Vérification des migrations..." -ForegroundColor Cyan
$migrationQuery = @"
SELECT COUNT(*) as migration_count
FROM "__EFMigrationsHistory"
WHERE "MigrationId" IS NOT NULL;
"@

$totalMigrations = 0
foreach ($service in $databases.Keys) {
    if ($connectionResults[$service]) {
        $dbConfig = $databases[$service]
        $migrationResult = Invoke-SqlQuery $migrationQuery $BaseHost $dbConfig.Port $dbConfig.Database $Username $Password
        if ($migrationResult) {
            $migrationCount = $migrationResult.Trim()
            $totalMigrations += [int]$migrationCount
            Write-Host "  📊 $service : $migrationCount migrations" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  $service : Impossible de vérifier les migrations" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ❌ $service : Base de données non accessible" -ForegroundColor Red
    }
}

Write-Host "📊 Nombre total de migrations appliquées: $totalMigrations" -ForegroundColor Green

# Statut final
if ($totalFound -eq $totalExpected -and $missingTables.Count -eq 0) {
    Write-Host "`n🎉 SUCCÈS: Toutes les tables attendues sont présentes!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n⚠️  ATTENTION: Des tables sont manquantes ou des problèmes ont été détectés" -ForegroundColor Yellow
    exit 1
}
