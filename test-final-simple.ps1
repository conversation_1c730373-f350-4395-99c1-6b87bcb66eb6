# Test simple et final pour les avis
Write-Host "Test Final - Avis et Commentaires" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

$identityUrl = "http://localhost:5155"
$reviewsUrl = "http://localhost:5006"
$catalogUrl = "http://localhost:5243"

# Créer un utilisateur unique
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testUser = "testuser$timestamp"
$testEmail = "test$<EMAIL>"

Write-Host "`n1. Creation utilisateur: $testUser" -ForegroundColor Cyan

$registerData = @{
    username = $testUser
    email = $testEmail
    password = "Test123!"
    firstName = "Test"
    lastName = "User"
    phoneNumber = "+224123456789"
} | ConvertTo-Json

try {
    $registerResponse = Invoke-RestMethod -Uri "$identityUrl/api/auth/register" -Method POST -Body $registerData -ContentType "application/json"
    Write-Host "✅ Utilisateur cree" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ Utilisateur existe peut-etre deja" -ForegroundColor Yellow
}

Start-Sleep -Seconds 3

Write-Host "`n2. Connexion..." -ForegroundColor Cyan

$loginData = @{
    identifier = $testUser
    password = "Test123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$identityUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    
    if ($loginResponse.accessToken) {
        Write-Host "✅ Connexion reussie !" -ForegroundColor Green
        Write-Host "   User ID: $($loginResponse.user.id)" -ForegroundColor Gray
        Write-Host "   Token: $($loginResponse.accessToken.Substring(0, 50))..." -ForegroundColor Gray
        
        $token = $loginResponse.accessToken
        $userId = $loginResponse.user.id
        $username = $loginResponse.user.username
        
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        Write-Host "`n3. Recuperation produits..." -ForegroundColor Cyan
        
        $products = Invoke-RestMethod -Uri "$catalogUrl/api/products" -Method GET
        
        if ($products -and $products.Count -gt 0) {
            $productId = $products[0].id
            $productName = $products[0].name
            
            Write-Host "✅ Produit trouve: $productName (ID: $productId)" -ForegroundColor Green
            
            Write-Host "`n4. Creation avis..." -ForegroundColor Cyan
            
            $reviewData = @{
                productId = [int]$productId
                userId = $userId.ToString()
                userName = $username
                rating = 5
                title = "Test automatique - Excellent produit"
                comment = "Ceci est un test automatique pour verifier les corrections JWT. Le produit est excellent !"
                isVerifiedPurchase = $false
            } | ConvertTo-Json
            
            Write-Host "   Envoi avis..." -ForegroundColor Gray
            
            try {
                $reviewResponse = Invoke-RestMethod -Uri "$reviewsUrl/api/reviews" -Method POST -Body $reviewData -Headers $headers
                
                Write-Host "✅ SUCCES ! Avis cree avec succes !" -ForegroundColor Green
                Write-Host "   Review ID: $($reviewResponse.id)" -ForegroundColor Gray
                Write-Host "   Titre: $($reviewResponse.title)" -ForegroundColor Gray
                Write-Host "   Note: $($reviewResponse.rating)/5" -ForegroundColor Gray
                Write-Host "   Statut: $($reviewResponse.status)" -ForegroundColor Gray
                
                Write-Host "`n✅ LES CORRECTIONS JWT FONCTIONNENT !" -ForegroundColor Green
                Write-Host "   L'authentification et la creation d'avis marchent parfaitement" -ForegroundColor Green
                
                # Verification rapide
                Start-Sleep -Seconds 2
                try {
                    $productReviews = Invoke-RestMethod -Uri "$reviewsUrl/api/reviews/product/$productId" -Method GET
                    Write-Host "   Verification: $($productReviews.reviews.Count) avis total(s) pour ce produit" -ForegroundColor Gray
                }
                catch {
                    Write-Host "   Verification impossible mais avis cree" -ForegroundColor Yellow
                }
                
            }
            catch {
                Write-Host "❌ ECHEC ! Erreur creation avis" -ForegroundColor Red
                Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
                
                if ($_.Exception.Response) {
                    $statusCode = $_.Exception.Response.StatusCode
                    Write-Host "   Code: $statusCode" -ForegroundColor Red
                    
                    if ($statusCode -eq 401) {
                        Write-Host "`n❌ PROBLEME JWT DETECTE !" -ForegroundColor Red
                        Write-Host "   Les corrections ne fonctionnent pas encore" -ForegroundColor Red
                    }
                }
            }
        }
        else {
            Write-Host "❌ Aucun produit trouve" -ForegroundColor Red
        }
    }
    else {
        Write-Host "❌ Pas de token recu" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Erreur connexion: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🏁 Test termine" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green
