-- Create Wishlist tables
CREATE TABLE IF NOT EXISTS "Wishlists" (
    "Id" SERIAL PRIMARY KEY,
    "UserId" VARCHAR(50) NOT NULL,
    "Name" VARCHAR(100),
    "Description" VARCHAR(500),
    "IsPublic" BOOLEAN DEFAULT FALSE,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "LastUpdated" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS "WishlistItems" (
    "Id" SERIAL PRIMARY KEY,
    "UserId" VARCHAR(50) NOT NULL,
    "ProductId" INTEGER NOT NULL,
    "ProductName" VARCHAR(100) NOT NULL,
    "ProductPrice" DECIMAL(18,2) NOT NULL,
    "Currency" VARCHAR(3) DEFAULT 'GNF',
    "ProductImageUrl" VARCHAR(500),
    "ProductBrand" VARCHAR(50),
    "CategoryName" VARCHAR(100),
    "IsAvailable" BOOLEAN DEFAULT TRUE,
    "AddedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "WishlistId" INTEGER,
    FOREIGN KEY ("WishlistId") REFERENCES "Wishlists"("Id") ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS "IX_Wishlists_UserId" ON "Wishlists"("UserId");
CREATE INDEX IF NOT EXISTS "IX_Wishlists_UserId_Name" ON "Wishlists"("UserId", "Name");
CREATE INDEX IF NOT EXISTS "IX_WishlistItems_UserId" ON "WishlistItems"("UserId");
CREATE UNIQUE INDEX IF NOT EXISTS "IX_WishlistItems_UserId_ProductId" ON "WishlistItems"("UserId", "ProductId");
CREATE INDEX IF NOT EXISTS "IX_WishlistItems_ProductId" ON "WishlistItems"("ProductId");
CREATE INDEX IF NOT EXISTS "IX_WishlistItems_AddedAt" ON "WishlistItems"("AddedAt");
CREATE INDEX IF NOT EXISTS "IX_WishlistItems_WishlistId" ON "WishlistItems"("WishlistId");

-- Update migration history
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion") 
VALUES ('20241210000001_InitialCreate', '9.0.0')
ON CONFLICT ("MigrationId") DO NOTHING;
