using NafaPlace.Delivery.Application.DTOs;
using NafaPlace.Delivery.Domain.Models;

namespace NafaPlace.Delivery.Application.Services;

public interface IDeliveryService
{
    // Zone Management
    Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync(bool activeOnly = true);
    Task<DeliveryZoneDto?> GetDeliveryZoneAsync(int id);
    Task<DeliveryZoneDto?> GetDeliveryZoneByCodeAsync(string code);
    Task<DeliveryZoneDto> CreateDeliveryZoneAsync(CreateDeliveryZoneRequest request);
    Task<DeliveryZoneDto> UpdateDeliveryZoneAsync(int id, UpdateDeliveryZoneRequest request);
    Task<bool> DeleteDeliveryZoneAsync(int id);

    // Carrier Management
    Task<List<CarrierDto>> GetCarriersAsync(bool activeOnly = true);
    Task<CarrierDto?> GetCarrierAsync(int id);
    Task<CarrierDto?> GetCarrierByCodeAsync(string code);
    Task<CarrierDto> CreateCarrierAsync(CreateCarrierRequest request);
    Task<CarrierDto> UpdateCarrierAsync(int id, UpdateCarrierRequest request);
    Task<bool> DeleteCarrierAsync(int id);

    // Carrier Zone Management
    Task<List<CarrierZoneDto>> GetCarrierZonesAsync(int? carrierId = null, int? zoneId = null);
    Task<CarrierZoneDto> CreateCarrierZoneAsync(CreateCarrierZoneRequest request);
    Task<CarrierZoneDto> UpdateCarrierZoneAsync(int id, UpdateCarrierZoneRequest request);
    Task<bool> DeleteCarrierZoneAsync(int id);

    // Delivery Quote and Calculation
    Task<List<DeliveryQuoteDto>> GetDeliveryQuotesAsync(DeliveryQuoteRequest request);
    Task<DeliveryQuoteDto?> GetBestDeliveryQuoteAsync(DeliveryQuoteRequest request);
    Task<decimal> CalculateDeliveryFeeAsync(string address, decimal orderValue, decimal? weight = null, DeliveryType type = DeliveryType.Standard);
    Task<DeliveryZoneDto?> FindDeliveryZoneAsync(string address, double? latitude = null, double? longitude = null);

    // Delivery Order Management
    Task<DeliveryOrderDto> CreateDeliveryOrderAsync(CreateDeliveryOrderRequest request);
    Task<DeliveryOrderDto?> GetDeliveryOrderAsync(int id);
    Task<DeliveryOrderDto?> GetDeliveryOrderByTrackingNumberAsync(string trackingNumber);
    Task<List<DeliveryOrderDto>> GetCustomerDeliveryOrdersAsync(string customerId, int page = 1, int pageSize = 20);
    Task<List<DeliveryOrderDto>> GetCarrierDeliveryOrdersAsync(int carrierId, int page = 1, int pageSize = 20);
    Task<bool> CancelDeliveryOrderAsync(int id, string reason);

    // Delivery Tracking
    Task<bool> UpdateDeliveryStatusAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request);
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingAsync(int deliveryOrderId);
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingByTrackingNumberAsync(string trackingNumber);
    Task<bool> AddTrackingEventAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request);

    // Route Management
    Task<DeliveryRouteDto> CreateDeliveryRouteAsync(CreateDeliveryRouteRequest request);
    Task<List<DeliveryRouteDto>> GetDeliveryRoutesAsync(int? carrierId = null, DateTime? date = null);
    Task<bool> OptimizeDeliveryRouteAsync(int routeId);
    Task<bool> AssignDeliveryToRouteAsync(int deliveryOrderId, int routeId, int sequence);

    // Analytics and Reporting
    Task<DeliveryStatsDto> GetDeliveryStatsAsync(DateTime? startDate = null, DateTime? endDate = null, int? carrierId = null);
    Task<List<CarrierPerformanceDto>> GetCarrierPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<ZonePerformanceDto>> GetZonePerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);

    // Customer Services
    Task<bool> RateDeliveryAsync(int deliveryOrderId, int rating, string? feedback = null);
    Task<bool> RequestDeliveryRescheduleAsync(int deliveryOrderId, DateTime newDate, string? reason = null);
    Task<List<DeliveryOrderDto>> GetDeliveriesNearLocationAsync(double latitude, double longitude, double radiusKm = 5);

    // Notifications and Alerts
    Task<bool> SendDeliveryNotificationAsync(int deliveryOrderId, string message, NotificationChannel channel = NotificationChannel.Email);
    Task<List<DeliveryOrderDto>> GetDelayedDeliveriesAsync();
    Task<List<DeliveryOrderDto>> GetFailedDeliveriesAsync();

    // Maintenance and Utilities
    Task<bool> RecalculateDeliveryFeesAsync();
    Task<int> CleanupOldTrackingEventsAsync(int daysToKeep = 90);
    Task<bool> SyncCarrierTrackingAsync(int carrierId);
}

// Additional DTOs for the service
public class CreateDeliveryZoneRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ZoneType Type { get; set; }
    public string? ParentZoneCode { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public double? Radius { get; set; }
    public decimal BaseDeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int EstimatedDeliveryDays { get; set; } = 1;
    public int MaxDeliveryDays { get; set; } = 7;
    public bool SameDayDeliveryAvailable { get; set; } = false;
    public decimal? SameDayDeliveryFee { get; set; }
    public bool ExpressDeliveryAvailable { get; set; } = false;
    public decimal? ExpressDeliveryFee { get; set; }
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public TimeSpan? DeliveryStartTime { get; set; }
    public TimeSpan? DeliveryEndTime { get; set; }
    public List<string> DeliveryDays { get; set; } = new();
}

public class UpdateDeliveryZoneRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public decimal? BaseDeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int? EstimatedDeliveryDays { get; set; }
    public int? MaxDeliveryDays { get; set; }
    public bool? SameDayDeliveryAvailable { get; set; }
    public decimal? SameDayDeliveryFee { get; set; }
    public bool? ExpressDeliveryAvailable { get; set; }
    public decimal? ExpressDeliveryFee { get; set; }
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public TimeSpan? DeliveryStartTime { get; set; }
    public TimeSpan? DeliveryEndTime { get; set; }
    public List<string>? DeliveryDays { get; set; }
}

public class CreateCarrierRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? LogoUrl { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string? Address { get; set; }
    public string? Website { get; set; }
    public CarrierType Type { get; set; }
    public string? ApiEndpoint { get; set; }
    public string? ApiKey { get; set; }
    public string? ApiSecret { get; set; }
}

public class UpdateCarrierRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? LogoUrl { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string? Address { get; set; }
    public string? Website { get; set; }
    public bool? IsActive { get; set; }
    public string? ApiEndpoint { get; set; }
    public string? ApiKey { get; set; }
    public string? ApiSecret { get; set; }
}

public class CreateCarrierZoneRequest
{
    public int CarrierId { get; set; }
    public int ZoneId { get; set; }
    public decimal DeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int EstimatedDeliveryDays { get; set; } = 1;
    public int MaxDeliveryDays { get; set; } = 7;
    public bool SameDayDeliveryAvailable { get; set; } = false;
    public decimal? SameDayDeliveryFee { get; set; }
    public bool ExpressDeliveryAvailable { get; set; } = false;
    public decimal? ExpressDeliveryFee { get; set; }
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public int Priority { get; set; } = 1;
}

public class UpdateCarrierZoneRequest
{
    public decimal? DeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int? EstimatedDeliveryDays { get; set; }
    public int? MaxDeliveryDays { get; set; }
    public bool? SameDayDeliveryAvailable { get; set; }
    public decimal? SameDayDeliveryFee { get; set; }
    public bool? ExpressDeliveryAvailable { get; set; }
    public decimal? ExpressDeliveryFee { get; set; }
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public int? Priority { get; set; }
    public bool? IsActive { get; set; }
}

public class DeliveryRouteDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public string? DriverPhone { get; set; }
    public DateTime RouteDate { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public RouteStatus Status { get; set; }
    public int TotalDeliveries { get; set; }
    public int CompletedDeliveries { get; set; }
    public int FailedDeliveries { get; set; }
    public decimal? TotalDistance { get; set; }
    public decimal? EstimatedDuration { get; set; }
    public decimal? ActualDuration { get; set; }
    public List<RouteDeliveryDto> Deliveries { get; set; } = new();
}

public class RouteDeliveryDto
{
    public int Id { get; set; }
    public int RouteId { get; set; }
    public int DeliveryOrderId { get; set; }
    public int Sequence { get; set; }
    public DateTime? EstimatedArrival { get; set; }
    public DateTime? ActualArrival { get; set; }
    public RouteDeliveryStatus Status { get; set; }
    public DeliveryOrderDto? DeliveryOrder { get; set; }
}

public class CreateDeliveryRouteRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public int CarrierId { get; set; }
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public string? DriverPhone { get; set; }
    public DateTime RouteDate { get; set; }
    public List<int> DeliveryOrderIds { get; set; } = new();
}

public class CarrierPerformanceDto
{
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public int TotalDeliveries { get; set; }
    public int SuccessfulDeliveries { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal AverageRating { get; set; }
    public decimal TotalRevenue { get; set; }
}

public class ZonePerformanceDto
{
    public int ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public int TotalDeliveries { get; set; }
    public int SuccessfulDeliveries { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal TotalRevenue { get; set; }
}

public enum NotificationChannel
{
    Email = 1,
    SMS = 2,
    Push = 3,
    InApp = 4
}
