# Script de test pour vérifier les corrections d'authentification JWT
Write-Host "🧪 Test des corrections d'authentification JWT" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost"
$identityPort = "5155"
$reviewsPort = "5006"
$wishlistPort = "5008"

# Données de test
$loginData = @{
    identifier = "lamine92"
    password = "Test123!"
} | ConvertTo-Json

Write-Host "`n1. 🔐 Test de connexion à l'API Identity..." -ForegroundColor Cyan

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl`:$identityPort/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    
    if ($loginResponse.accessToken) {
        $token = $loginResponse.accessToken
        Write-Host "✅ Connexion réussie ! Token obtenu." -ForegroundColor Green
        Write-Host "   Token: $($token.Substring(0, 50))..." -ForegroundColor Gray
        
        # Headers d'authentification
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        Write-Host "`n2. 🔍 Test de l'API Reviews avec authentification..." -ForegroundColor Cyan
        
        # Test création d'un avis
        $reviewData = @{
            productId = 1
            userId = "4"
            userName = "lamine92"
            rating = 5
            title = "Test automatique"
            comment = "Ceci est un test automatique des corrections JWT"
            isVerifiedPurchase = $false
        } | ConvertTo-Json
        
        try {
            $reviewResponse = Invoke-RestMethod -Uri "$baseUrl`:$reviewsPort/api/reviews" -Method POST -Body $reviewData -Headers $headers
            Write-Host "✅ API Reviews: Création d'avis réussie !" -ForegroundColor Green
            Write-Host "   Review ID: $($reviewResponse.id)" -ForegroundColor Gray
        }
        catch {
            Write-Host "❌ API Reviews: Erreur lors de la création d'avis" -ForegroundColor Red
            Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n3. 💝 Test de l'API Wishlist avec authentification..." -ForegroundColor Cyan
        
        try {
            $wishlistResponse = Invoke-RestMethod -Uri "$baseUrl`:$wishlistPort/api/wishlist/count" -Method GET -Headers $headers
            Write-Host "✅ API Wishlist: Récupération du compteur réussie !" -ForegroundColor Green
            Write-Host "   Nombre d'articles: $wishlistResponse" -ForegroundColor Gray
        }
        catch {
            Write-Host "❌ API Wishlist: Erreur lors de la récupération du compteur" -ForegroundColor Red
            Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Test ajout à la wishlist
        try {
            $wishlistAddData = @{
                productId = 1
            } | ConvertTo-Json
            
            $addResponse = Invoke-RestMethod -Uri "$baseUrl`:$wishlistPort/api/wishlist/add" -Method POST -Body $wishlistAddData -Headers $headers
            Write-Host "✅ API Wishlist: Ajout de produit réussi !" -ForegroundColor Green
        }
        catch {
            Write-Host "⚠️ API Wishlist: Produit déjà dans la wishlist ou autre erreur" -ForegroundColor Yellow
            Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
    } else {
        Write-Host "❌ Connexion échouée: Pas de token reçu" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Erreur lors de la connexion" -ForegroundColor Red
    Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
    
    # Vérifier si l'API Identity est accessible
    try {
        $healthCheck = Invoke-RestMethod -Uri "$baseUrl`:$identityPort/" -Method GET
        Write-Host "ℹ️ API Identity est accessible mais la connexion a échoué" -ForegroundColor Blue
    }
    catch {
        Write-Host "❌ API Identity n'est pas accessible" -ForegroundColor Red
    }
}

Write-Host "`n4. 📊 Test des endpoints publics..." -ForegroundColor Cyan

# Test endpoint public Reviews
try {
    $publicReview = Invoke-RestMethod -Uri "$baseUrl`:$reviewsPort/api/reviews/product/1/summary" -Method GET
    Write-Host "✅ API Reviews (public): Récupération du résumé réussie !" -ForegroundColor Green
    Write-Host "   Produit ID: $($publicReview.productId), Note moyenne: $($publicReview.averageRating)" -ForegroundColor Gray
}
catch {
    Write-Host "❌ API Reviews (public): Erreur" -ForegroundColor Red
    Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🏁 Test terminé !" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

Write-Host "`n📋 Instructions pour tester manuellement:" -ForegroundColor Yellow
Write-Host "1. Ouvrez http://localhost:8080 dans votre navigateur" -ForegroundColor White
Write-Host "2. Connectez-vous avec: lamine92 / Test123!" -ForegroundColor White
Write-Host "3. Allez sur une page produit et essayez de créer un avis" -ForegroundColor White
Write-Host "4. Vérifiez que la wishlist fonctionne" -ForegroundColor White
