# Script de test complet : Création client + Connexion + Commentaire
Write-Host "🧪 Test complet du flux d'authentification et commentaires" -ForegroundColor Green
Write-Host "==========================================================" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost"
$identityPort = "5155"
$reviewsPort = "5006"
$wishlistPort = "5008"
$catalogPort = "5243"

# Générer des données uniques pour le test
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testUsername = "testuser$timestamp"
$testEmail = "test$<EMAIL>"

Write-Host "`n1. 👤 Création d'un nouveau client..." -ForegroundColor Cyan

# Données pour créer un nouveau client
$registerData = @{
    username = $testUsername
    email = $testEmail
    password = "Test123!"
    firstName = "Test"
    lastName = "User"
    phoneNumber = "+224123456789"
} | ConvertTo-<PERSON><PERSON>

try {
    Write-Host "   Tentative de création du client: $testUsername" -ForegroundColor Gray
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl`:$identityPort/api/auth/register" -Method POST -Body $registerData -ContentType "application/json"
    
    Write-Host "✅ Client créé avec succès !" -ForegroundColor Green
    Write-Host "   Username: $testUsername" -ForegroundColor Gray
    Write-Host "   Email: $testEmail" -ForegroundColor Gray
    
    # Attendre un peu pour que l'utilisateur soit bien créé
    Start-Sleep -Seconds 2
    
    Write-Host "`n2. 🔐 Connexion avec le nouveau client..." -ForegroundColor Cyan
    
    # Données de connexion
    $loginData = @{
        identifier = $testUsername
        password = "Test123!"
    } | ConvertTo-Json
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl`:$identityPort/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
        
        if ($loginResponse.accessToken) {
            $token = $loginResponse.accessToken
            $userId = $loginResponse.user.id
            
            Write-Host "✅ Connexion réussie !" -ForegroundColor Green
            Write-Host "   User ID: $userId" -ForegroundColor Gray
            Write-Host "   Token: $($token.Substring(0, 50))..." -ForegroundColor Gray
            
            # Headers d'authentification
            $headers = @{
                "Authorization" = "Bearer $token"
                "Content-Type" = "application/json"
            }
            
            Write-Host "`n3. 📦 Vérification des produits disponibles..." -ForegroundColor Cyan
            
            try {
                $products = Invoke-RestMethod -Uri "$baseUrl`:$catalogPort/api/products" -Method GET
                
                if ($products -and $products.Count -gt 0) {
                    $productId = $products[0].id
                    $productName = $products[0].name
                    
                    Write-Host "✅ Produits trouvés !" -ForegroundColor Green
                    Write-Host "   Test avec le produit: $productName (ID: $productId)" -ForegroundColor Gray
                    
                    Write-Host "`n4. ⭐ Création d'un commentaire/avis..." -ForegroundColor Cyan
                    
                    # Données pour créer un avis
                    $reviewData = @{
                        productId = $productId
                        userId = $userId.ToString()
                        userName = $testUsername
                        rating = 5
                        title = "Excellent produit - Test automatique"
                        comment = "Ceci est un test automatique de création d'avis après les corrections JWT. Le produit est fantastique !"
                        isVerifiedPurchase = $false
                    } | ConvertTo-Json
                    
                    try {
                        Write-Host "   Envoi de l'avis pour le produit ID: $productId" -ForegroundColor Gray
                        $reviewResponse = Invoke-RestMethod -Uri "$baseUrl`:$reviewsPort/api/reviews" -Method POST -Body $reviewData -Headers $headers
                        
                        Write-Host "✅ Avis créé avec succès !" -ForegroundColor Green
                        Write-Host "   Review ID: $($reviewResponse.id)" -ForegroundColor Gray
                        Write-Host "   Titre: $($reviewResponse.title)" -ForegroundColor Gray
                        Write-Host "   Note: $($reviewResponse.rating)/5" -ForegroundColor Gray
                        
                        Write-Host "`n5. 🔍 Vérification de l'avis créé..." -ForegroundColor Cyan
                        
                        # Vérifier que l'avis apparaît dans la liste
                        try {
                            $productReviews = Invoke-RestMethod -Uri "$baseUrl`:$reviewsPort/api/reviews/product/$productId" -Method GET
                            
                            $ourReview = $productReviews.reviews | Where-Object { $_.id -eq $reviewResponse.id }
                            
                            if ($ourReview) {
                                Write-Host "✅ Avis trouvé dans la liste des avis du produit !" -ForegroundColor Green
                                Write-Host "   Statut: $($ourReview.status)" -ForegroundColor Gray
                            } else {
                                Write-Host "⚠️ Avis créé mais pas encore visible (peut-être en attente de modération)" -ForegroundColor Yellow
                            }
                        }
                        catch {
                            Write-Host "⚠️ Erreur lors de la vérification de l'avis" -ForegroundColor Yellow
                            Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Yellow
                        }
                        
                        Write-Host "`n6. 💝 Test de la wishlist..." -ForegroundColor Cyan
                        
                        try {
                            # Test du compteur wishlist
                            $wishlistCount = Invoke-RestMethod -Uri "$baseUrl`:$wishlistPort/api/wishlist/count" -Method GET -Headers $headers
                            Write-Host "✅ Wishlist accessible ! Nombre d'articles: $wishlistCount" -ForegroundColor Green
                            
                            # Test d'ajout à la wishlist
                            $wishlistAddData = @{
                                productId = $productId
                            } | ConvertTo-Json
                            
                            try {
                                $addResponse = Invoke-RestMethod -Uri "$baseUrl`:$wishlistPort/api/wishlist/add" -Method POST -Body $wishlistAddData -Headers $headers
                                Write-Host "✅ Produit ajouté à la wishlist !" -ForegroundColor Green
                            }
                            catch {
                                Write-Host "⚠️ Produit déjà dans la wishlist ou erreur d'ajout" -ForegroundColor Yellow
                            }
                        }
                        catch {
                            Write-Host "❌ Erreur avec la wishlist" -ForegroundColor Red
                            Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
                        }
                        
                    }
                    catch {
                        Write-Host "❌ Erreur lors de la création de l'avis" -ForegroundColor Red
                        Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
                        
                        # Afficher plus de détails sur l'erreur
                        if ($_.Exception.Response) {
                            $statusCode = $_.Exception.Response.StatusCode
                            Write-Host "   Code de statut: $statusCode" -ForegroundColor Red
                        }
                    }
                    
                } else {
                    Write-Host "❌ Aucun produit trouvé pour tester" -ForegroundColor Red
                }
            }
            catch {
                Write-Host "❌ Erreur lors de la récupération des produits" -ForegroundColor Red
                Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
            }

        } else {
            Write-Host "❌ Connexion échouée: Pas de token reçu" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Erreur lors de la connexion" -ForegroundColor Red
        Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
    }
    
}
catch {
    Write-Host "❌ Erreur lors de la création du client" -ForegroundColor Red
    Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
    
    # Si l'utilisateur existe déjà, essayer de se connecter
    if ($_.Exception.Message -like "*already exists*" -or $_.Exception.Message -like "*déjà*") {
        Write-Host "`n⚠️ L'utilisateur existe peut-être déjà, tentative de connexion..." -ForegroundColor Yellow
        
        $loginData = @{
            identifier = $testUsername
            password = "Test123!"
        } | ConvertTo-Json
        
        try {
            $loginResponse = Invoke-RestMethod -Uri "$baseUrl`:$identityPort/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
            Write-Host "✅ Connexion réussie avec l'utilisateur existant !" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Connexion également échouée" -ForegroundColor Red
        }
    }
}

Write-Host "`n🏁 Test terminé !" -ForegroundColor Green
Write-Host "==========================================================" -ForegroundColor Green

Write-Host "`n📋 Résumé des tests:" -ForegroundColor Yellow
Write-Host "- Création de client" -ForegroundColor White
Write-Host "- Authentification JWT" -ForegroundColor White
Write-Host "- Creation d'avis/commentaire" -ForegroundColor White
Write-Host "- Test de la wishlist" -ForegroundColor White
Write-Host "`nSi tous les tests sont ✅, les corrections JWT fonctionnent !" -ForegroundColor Green
