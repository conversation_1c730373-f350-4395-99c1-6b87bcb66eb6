-- Vérifier la nouvelle commande #00000032 (ID = 32) et ses OrderItems
SELECT o."Id", o."UserId", o."OrderDate", o."TotalAmount", o."Status", 
       oi."ProductId", oi."SellerId", oi."Quantity", oi."UnitPrice"
FROM "Orders" o
JOIN "OrderItems" oi ON o."Id" = oi."OrderId"
WHERE o."Id" = 32;

-- Vérifier aussi toutes les commandes récentes pour le produit "Sac noir" (ID = 19)
SELECT o."Id", o."UserId", o."OrderDate", o."TotalAmount", o."Status", 
       oi."ProductId", oi."SellerId", oi."Quantity", oi."UnitPrice"
FROM "Orders" o
JOIN "OrderItems" oi ON o."Id" = oi."OrderId"
WHERE oi."ProductId" = 19
ORDER BY o."OrderDate" DESC;
