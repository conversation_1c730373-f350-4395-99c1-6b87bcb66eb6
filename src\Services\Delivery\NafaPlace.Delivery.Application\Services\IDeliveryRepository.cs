using NafaPlace.Delivery.Application.DTOs;

namespace NafaPlace.Delivery.Application.Services;

public interface IDeliveryRepository
{
    Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync();
    Task<DeliveryZoneDto?> GetDeliveryZoneByIdAsync(int zoneId);
    Task<DeliveryOrderDto> CreateDeliveryOrderAsync(CreateDeliveryOrderRequest request);
    Task<DeliveryOrderDto?> GetDeliveryOrderByIdAsync(int id);
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingByTrackingNumberAsync(string trackingNumber);
    Task<bool> UpdateDeliveryStatusAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request);
}
