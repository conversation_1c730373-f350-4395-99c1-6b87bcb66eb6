@page "/order-confirmation/{OrderId:int}"
@using NafaPlace.Web.Models.Order
@using NafaPlace.Web.Services
@inject IOrderService OrderService
@inject NavigationManager NavigationManager

<PageTitle>Confirmation de commande - NafaPlace</PageTitle>

<div class="container my-5">
    @if (_order != null)
    {
        <!-- En-tête de confirmation -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="text-center mb-5">
                    <div class="success-icon mb-3">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h1 class="text-success mb-3">Commande confirmée !</h1>
                    <p class="lead text-muted">
                        Merci pour votre commande. Nous avons bien reçu votre demande et nous la traitons actuellement.
                    </p>
                </div>

                <!-- Détails de la commande -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-receipt me-2"></i>
                            Détails de la commande
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Numéro de commande :</strong> #@_order.Id.ToString().PadLeft(8, '0')</p>
                                <p><strong>Date :</strong> @_order.OrderDate.ToString("dd/MM/yyyy à HH:mm")</p>
                                <p><strong>Statut :</strong>
                                    <span class="badge bg-warning">@GetStatusText(_order.Status)</span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Total :</strong> <span class="text-primary fs-5">@_order.TotalAmount.ToString("N0") @_order.Currency</span></p>
                                <p><strong>Mode de paiement :</strong> À la livraison</p>
                                <p><strong>Livraison estimée :</strong> 2-3 jours ouvrables</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Articles commandés -->
                @if (_order.OrderItems != null && _order.OrderItems.Any())
                {
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-box me-2"></i>
                                Articles commandés (@_order.OrderItems.Count())
                            </h6>
                        </div>
                        <div class="card-body">
                            @foreach (var item in _order.OrderItems)
                            {
                                <div class="d-flex justify-content-between align-items-center py-2 @(item != _order.OrderItems.Last() ? "border-bottom" : "")">
                                    <div>
                                        <h6 class="mb-1">@item.ProductName</h6>
                                        <small class="text-muted">Quantité : @item.Quantity</small>
                                    </div>
                                    <div class="text-end">
                                        <div>@item.UnitPrice.ToString("N0") GNF × @item.Quantity</div>
                                        <strong>@((item.UnitPrice * item.Quantity).ToString("N0")) GNF</strong>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Prochaines étapes -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Prochaines étapes
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item completed">
                                <i class="fas fa-check-circle text-success"></i>
                                <span>Commande reçue et confirmée</span>
                            </div>
                            <div class="timeline-item">
                                <i class="fas fa-clock text-warning"></i>
                                <span>Préparation de votre commande</span>
                            </div>
                            <div class="timeline-item">
                                <i class="fas fa-truck text-muted"></i>
                                <span>Expédition et livraison</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="text-center">

                    @* Forcer l'affichage du bouton Stripe pour test *@
                    @if (true) @* Condition temporaire pour forcer l'affichage *@
                    {
                        <button class="btn btn-success btn-lg me-3" @onclick="ProcessStripePayment">
                            <i class="fab fa-cc-stripe me-2"></i>
                            Payer avec Stripe (@_order.TotalAmount.ToString("N0") GNF)
                        </button>
                        <button class="btn btn-warning btn-lg me-3" @onclick="ProcessOrangeMoneyPayment">
                            <i class="fas fa-mobile-alt me-2"></i>
                            Payer avec Orange Money (@_order.TotalAmount.ToString("N0") GNF)
                        </button>
                        <button class="btn btn-outline-secondary" @onclick="ContinueShopping">
                            <i class="fas fa-shopping-bag me-2"></i>
                            Continuer mes achats
                        </button>
                        <button class="btn btn-outline-info" @onclick="ViewOrders">
                            <i class="fas fa-list me-2"></i>
                            Voir mes commandes
                        </button>
                    }
                </div>
            </div>
        </div>
    }
    else if (_loading)
    {
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-3">Chargement des détails de la commande...</p>
        </div>
    }
    else
    {
        <div class="text-center">
            <div class="alert alert-danger">
                <h4>Commande introuvable</h4>
                <p>La commande demandée n'existe pas ou n'est plus accessible.</p>
                <button class="btn btn-primary" @onclick="ContinueShopping">
                    Retour à l'accueil
                </button>
            </div>
        </div>
    }
</div>

<style>
    .timeline {
        list-style: none;
        padding: 0;
    }

    .timeline-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .timeline-item i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
    }

    .timeline-item.completed {
        color: #198754;
    }
</style>

@code {
    [Parameter] public int OrderId { get; set; }

    private OrderDto? _order;
    private bool _loading = true;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _order = await OrderService.GetOrderByIdAsync(OrderId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de la commande: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "Pending" => "En attente",
            "Processing" => "En cours de traitement",
            "Shipped" => "Expédiée",
            "Delivered" => "Livrée",
            "Cancelled" => "Annulée",
            _ => status
        };
    }

    private async Task ProcessStripePayment()
    {
        try
        {
            // Rediriger vers la page de paiement Stripe
            NavigationManager.NavigateTo($"/payment/stripe/{OrderId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la redirection vers Stripe: {ex.Message}");
        }
    }

    private async Task ProcessOrangeMoneyPayment()
    {
        try
        {
            // Rediriger vers la page de paiement Orange Money
            NavigationManager.NavigateTo($"/payment/orange-money/{OrderId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la redirection vers Orange Money: {ex.Message}");
        }
    }

    private void ContinueShopping()
    {
        NavigationManager.NavigateTo("/");
    }

    private void ViewOrders()
    {
        NavigationManager.NavigateTo("/orders");
    }

    private void GoToPayment()
    {
        NavigationManager.NavigateTo($"/payment/simple/{OrderId}");
    }

    private void ViewOrderDetails()
    {
        NavigationManager.NavigateTo($"/account/orders/{OrderId}");
    }
}