# Script de test simple pour vérifier l'authentification JWT
Write-Host "Test d'authentification JWT" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost"
$identityPort = "5155"
$reviewsPort = "5006"
$wishlistPort = "5008"
$catalogPort = "5243"

# Générer des données uniques
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testUsername = "testuser$timestamp"
$testEmail = "test$<EMAIL>"

Write-Host "`n1. Creation d'un nouveau client..." -ForegroundColor Cyan

# Données pour créer un client
$registerData = @{
    username = $testUsername
    email = $testEmail
    password = "Test123!"
    firstName = "Test"
    lastName = "User"
    phoneNumber = "+224123456789"
} | ConvertTo-<PERSON><PERSON>

try {
    Write-Host "   Tentative de creation: $testUsername" -ForegroundColor Gray
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl`:$identityPort/api/auth/register" -Method POST -Body $registerData -ContentType "application/json"
    
    Write-Host "✅ Client cree avec succes !" -ForegroundColor Green
    Write-Host "   Username: $testUsername" -ForegroundColor Gray
    
    # Attendre un peu
    Start-Sleep -Seconds 2
    
    Write-Host "`n2. Connexion..." -ForegroundColor Cyan
    
    # Données de connexion
    $loginData = @{
        identifier = $testUsername
        password = "Test123!"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl`:$identityPort/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    
    if ($loginResponse.accessToken) {
        $token = $loginResponse.accessToken
        $userId = $loginResponse.user.id
        
        Write-Host "✅ Connexion reussie !" -ForegroundColor Green
        Write-Host "   User ID: $userId" -ForegroundColor Gray
        Write-Host "   Token: $($token.Substring(0, 50))..." -ForegroundColor Gray
        
        # Headers d'authentification
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        Write-Host "`n3. Test des produits..." -ForegroundColor Cyan
        
        $products = Invoke-RestMethod -Uri "$baseUrl`:$catalogPort/api/products" -Method GET
        
        if ($products -and $products.Count -gt 0) {
            $productId = $products[0].id
            $productName = $products[0].name
            
            Write-Host "✅ Produits trouves !" -ForegroundColor Green
            Write-Host "   Test avec: $productName (ID: $productId)" -ForegroundColor Gray
            
            Write-Host "`n4. Creation d'un avis..." -ForegroundColor Cyan
            
            # Données pour créer un avis
            $reviewData = @{
                productId = $productId
                userId = $userId.ToString()
                userName = $testUsername
                rating = 5
                title = "Excellent produit - Test automatique"
                comment = "Ceci est un test automatique de creation d'avis apres les corrections JWT."
                isVerifiedPurchase = $false
            } | ConvertTo-Json
            
            Write-Host "   Envoi de l'avis pour le produit ID: $productId" -ForegroundColor Gray
            $reviewResponse = Invoke-RestMethod -Uri "$baseUrl`:$reviewsPort/api/reviews" -Method POST -Body $reviewData -Headers $headers
            
            Write-Host "✅ Avis cree avec succes !" -ForegroundColor Green
            Write-Host "   Review ID: $($reviewResponse.id)" -ForegroundColor Gray
            Write-Host "   Titre: $($reviewResponse.title)" -ForegroundColor Gray
            Write-Host "   Note: $($reviewResponse.rating)/5" -ForegroundColor Gray
            
            Write-Host "`n5. Test de la wishlist..." -ForegroundColor Cyan
            
            # Test du compteur wishlist
            $wishlistCount = Invoke-RestMethod -Uri "$baseUrl`:$wishlistPort/api/wishlist/count" -Method GET -Headers $headers
            Write-Host "✅ Wishlist accessible ! Nombre d'articles: $wishlistCount" -ForegroundColor Green
            
            # Test d'ajout à la wishlist
            $wishlistAddData = @{
                productId = $productId
            } | ConvertTo-Json
            
            try {
                $addResponse = Invoke-RestMethod -Uri "$baseUrl`:$wishlistPort/api/wishlist/add" -Method POST -Body $wishlistAddData -Headers $headers
                Write-Host "✅ Produit ajoute a la wishlist !" -ForegroundColor Green
            }
            catch {
                Write-Host "⚠️ Produit deja dans la wishlist" -ForegroundColor Yellow
            }
            
        }
        else {
            Write-Host "❌ Aucun produit trouve" -ForegroundColor Red
        }
    }
    else {
        Write-Host "❌ Pas de token recu" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
    
    # Afficher plus de détails
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "   Code de statut: $statusCode" -ForegroundColor Red
    }
}

Write-Host "`n🏁 Test termine !" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

Write-Host "`nResume des tests:" -ForegroundColor Yellow
Write-Host "- Creation de client" -ForegroundColor White
Write-Host "- Authentification JWT" -ForegroundColor White
Write-Host "- Creation d'avis/commentaire" -ForegroundColor White
Write-Host "- Test de la wishlist" -ForegroundColor White
Write-Host "`nSi tous les tests sont ✅, les corrections JWT fonctionnent !" -ForegroundColor Green
