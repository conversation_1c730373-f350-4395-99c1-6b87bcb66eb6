{"ApiEndpoints": {"IdentityApi": "https://nafaplace-test.fly.dev/api/identity", "CatalogApi": "https://nafaplace-test.fly.dev/api/catalog", "CartApi": "https://nafaplace-test.fly.dev/api/cart", "OrderApi": "https://nafaplace-test.fly.dev/api/orders", "PaymentApi": "https://nafaplace-test.fly.dev/api/payments", "ReviewApi": "https://nafaplace-test.fly.dev/api/reviews", "NotificationsApi": "https://nafaplace-test.fly.dev/api/notifications", "WishlistApi": "https://nafaplace-test.fly.dev/api/wishlist", "CouponsApi": "http://localhost:5009/api/coupon"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Cloudinary": {"CloudName": "${CLOUDINARY_CLOUD_NAME}", "ApiKey": "${CLOUDINARY_API_KEY}", "ApiSecret": "${CLOUDINARY_API_SECRET}"}, "Stripe": {"PublishableKey": "${STRIPE_PUBLISHABLE_KEY}", "SecretKey": "${STRIPE_SECRET_KEY}"}}