using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.Web.Models.Wishlist;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;

namespace NafaPlace.Web.Services;

public class WishlistService : IWishlistService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<WishlistService> _logger;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILocalStorageService _localStorage;

    public WishlistService(HttpClient httpClient, ILogger<WishlistService> logger,
        AuthenticationStateProvider authStateProvider, ILocalStorageService localStorage)
    {
        _httpClient = httpClient;
        _logger = logger;
        _authStateProvider = authStateProvider;
        _localStorage = localStorage;
    }

    private async Task SetAuthorizationHeaderAsync()
    {
        try
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var token = await _localStorage.GetItemAsync<string>("authToken");
                if (!string.IsNullOrEmpty(token))
                {
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to set authorization header");
        }
    }

    public async Task<WishlistDto> GetUserWishlistAsync(string userId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.GetAsync("api/wishlist");
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            var wishlist = JsonSerializer.Deserialize<WishlistDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return wishlist ?? new WishlistDto { UserId = userId };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user wishlist");
            return new WishlistDto { UserId = userId };
        }
    }

    public async Task<WishlistSummaryDto> GetWishlistSummaryAsync(string userId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.GetAsync("api/wishlist/summary");
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            var summary = JsonSerializer.Deserialize<WishlistSummaryDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return summary ?? new WishlistSummaryDto { UserId = userId };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist summary");
            return new WishlistSummaryDto { UserId = userId };
        }
    }

    public async Task<int> GetWishlistItemCountAsync(string userId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.GetAsync("api/wishlist/count");
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<int>(content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist item count");
            return 0;
        }
    }

    public async Task<List<WishlistItemDto>> GetWishlistItemsAsync(string userId, int page = 1, int pageSize = 20)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/wishlist/items?page={page}&pageSize={pageSize}");
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            var items = JsonSerializer.Deserialize<List<WishlistItemDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return items ?? new List<WishlistItemDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist items");
            return new List<WishlistItemDto>();
        }
    }

    public async Task<WishlistItemDto> AddToWishlistAsync(string userId, AddToWishlistRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/wishlist/items", request);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            var item = JsonSerializer.Deserialize<WishlistItemDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return item ?? throw new InvalidOperationException("Failed to add item to wishlist");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to wishlist");
            throw;
        }
    }

    public async Task<bool> RemoveFromWishlistAsync(string userId, int productId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"api/wishlist/items/{productId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing item from wishlist");
            return false;
        }
    }

    public async Task<bool> IsProductInWishlistAsync(string userId, int productId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/wishlist/items/{productId}/exists");
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<bool>(content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if product is in wishlist");
            return false;
        }
    }

    public async Task<WishlistItemDto?> GetWishlistItemAsync(string userId, int productId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/wishlist/items/{productId}");
            if (!response.IsSuccessStatusCode)
                return null;
            
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<WishlistItemDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist item");
            return null;
        }
    }

    public async Task<bool> ClearWishlistAsync(string userId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync("api/wishlist/clear");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing wishlist");
            return false;
        }
    }

    public async Task<bool> MoveToCartAsync(string userId, int productId)
    {
        try
        {
            var response = await _httpClient.PostAsync($"api/wishlist/items/{productId}/move-to-cart", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving item to cart");
            return false;
        }
    }

    public async Task<List<WishlistItemDto>> GetRecentlyAddedItemsAsync(string userId, int count = 5)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/wishlist/recent?count={count}");
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            var items = JsonSerializer.Deserialize<List<WishlistItemDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return items ?? new List<WishlistItemDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recently added items");
            return new List<WishlistItemDto>();
        }
    }

    public async Task<WishlistDto> GetGuestWishlistAsync(string guestId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/wishlist/guest/{guestId}");
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            var wishlist = JsonSerializer.Deserialize<WishlistDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return wishlist ?? new WishlistDto { UserId = $"guest_{guestId}" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting guest wishlist");
            return new WishlistDto { UserId = $"guest_{guestId}" };
        }
    }

    public async Task<WishlistItemDto> AddToGuestWishlistAsync(string guestId, AddToWishlistRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync($"api/wishlist/guest/{guestId}/items", request);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            var item = JsonSerializer.Deserialize<WishlistItemDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return item ?? throw new InvalidOperationException("Failed to add item to guest wishlist");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to guest wishlist");
            throw;
        }
    }
}
