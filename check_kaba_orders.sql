-- 1. Trouver l'ID de l'utilisateur <EMAIL> dans la base Identity
-- (Nous savons déjà que c'est UserId = 16 d'après les logs précédents)

-- 2. Vérifier toutes les commandes pour UserId = '16'
SELECT o."Id", o."UserId", o."OrderDate", o."TotalAmount", o."Status",
       oi."ProductId", oi."SellerId", oi."Quantity", oi."UnitPrice"
FROM "Orders" o
JOIN "OrderItems" oi ON o."Id" = oi."OrderId"
WHERE o."UserId" = '16'
ORDER BY o."OrderDate" DESC;

-- 3. Compter le nombre total de commandes pour cet utilisateur
SELECT COUNT(*) as "NombreCommandes"
FROM "Orders"
WHERE "UserId" = '16';

-- 4. Vérifier les commandes récentes (dernières 10)
SELECT o."Id", o."UserId", o."OrderDate", o."TotalAmount", o."Status"
FROM "Orders" o
WHERE o."UserId" = '16'
ORDER BY o."OrderDate" DESC
LIMIT 10;
